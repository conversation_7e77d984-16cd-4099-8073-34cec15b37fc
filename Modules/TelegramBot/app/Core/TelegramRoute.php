<?php

namespace Modules\TelegramBot\Core;

use Illuminate\Contracts\Container\Container;
use Closure;

class TelegramRoute
{
    public string $key;
    public $pattern;
    public array $middleware = [];
    public array $wheres = [];
    protected Container $app;
    protected $handler;
    protected ?string $namespace;

    public function __construct(Container $app, $pattern, $handler, array $attrs = [])
    {
        $this->app = $app;
        $this->pattern = $pattern;
        $this->handler = $handler;
        $this->key = is_string($pattern) ? $pattern : spl_object_id($this).':callable';
        $this->namespace = $attrs['namespace'] ?? null;
        $this->middleware = $attrs['middleware'] ?? [];
    }

    public function middleware(array|string $mw): self
    {
        $this->middleware = array_merge($this->middleware, (array) $mw);
        return $this;
    }

    public function where(string $name, string $regex): self
    {
        $this->wheres[$name] = $regex;
        return $this;
    }

    public function whereNumber(string $name): self
    {
        return $this->where($name, '\d+');
    }

    public function whereAlpha(string $name): self
    {
        return $this->where($name, '[A-Za-z]+');
    }

    public function invoke(array $ctx)
    {
        $callable = $this->makeCallable($this->handler, $this->namespace);
        $paramCount = $this->getParamCount($callable);

        if ($paramCount === 0) {
            return $callable();
        }

        if ($paramCount === 1) {
            return $callable($ctx);
        }

        $user   = $ctx['user']   ?? null;
        $params = $ctx['params'] ?? [];
        return $callable($user, $params, $ctx);
    }

    /** @return callable */
    protected function makeCallable($handler, ?string $namespace)
    {
        if (is_string($handler) && $namespace && !str_contains($handler, '\\')) {
            $handler = trim($namespace.'\\'.$handler, '\\');
        }

        // Class::class (invokable)
        if (is_string($handler) && class_exists($handler)) {
            $instance = $this->app->make($handler);
            if (is_callable($instance)) {
                return $instance;
            }
            throw new \RuntimeException("Handler class {$handler} is not invokable.");
        }

        // [ClassString, 'method']
        if (is_array($handler) && isset($handler[0]) && is_string($handler[0])) {
            $handler[0] = $this->app->make($handler[0]);
        }

        if ($handler instanceof Closure || is_callable($handler)) {
            return $handler;
        }

        throw new \RuntimeException('Invalid Telegram route handler');
    }

    protected function getParamCount(callable $callable): int
    {
        if (is_array($callable)) {
            $ref = new \ReflectionMethod($callable[0], $callable[1]);
        } elseif (is_object($callable) && !($callable instanceof \Closure)) {
            $ref = new \ReflectionMethod($callable, '__invoke');
        } else {
            $ref = new \ReflectionFunction($callable);
        }

        return $ref->getNumberOfParameters();
    }
}
