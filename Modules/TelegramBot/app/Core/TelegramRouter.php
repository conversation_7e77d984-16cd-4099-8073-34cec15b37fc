<?php

namespace Modules\TelegramBot\Core;

use App\Helpers\DiscordLogHelper;
use Illuminate\Contracts\Container\Container;
use Illuminate\Pipeline\Pipeline;
use Illuminate\Support\Str;
use Closure;
use Modules\TelegramBot\Support\TelegramContext;
use Mo<PERSON>les\TelegramBot\Models\TelegramUser;
use Modules\TelegramBot\Services\WebhookService;

class TelegramRouter
{
    protected Container $app;

    /** @var array<string, array> */
    protected array $routes = [
        'command'  => [],
        'callback' => [],
        'text'     => [],
        'state'    => [],
        'payment'  => [],
        'any'      => [],
        'fallback' => null,
    ];

    protected array $globalMiddleware = [];

    protected array $groupStack = [];

    public function __construct(Container $app)
    {
        $this->app = $app;
    }

    /* ---------- Route DSL ---------- */

    public function group(array $attrs, Closure $callback): void
    {
        $this->groupStack[] = $attrs;
        $callback();
        array_pop($this->groupStack);
    }

    public function command(string $name, $handler): TelegramRoute
    {
        return $this->add('command', $this->normalizeCommand($name), $handler);
    }

    public function callback(string $pattern, $handler): TelegramRoute
    {
        return $this->add('callback', $pattern, $handler);
    }

    public function text($pattern, $handler): TelegramRoute
    {
        // $pattern có thể là regex string hoặc callable matcher
        return $this->add('text', $pattern, $handler);
    }

    public function state(string $stateKey, $handler): TelegramRoute
    {
        return $this->add('state', $stateKey, $handler);
    }

    public function payment(string $type, $handler): TelegramRoute
    {
        return $this->add('payment', $type, $handler);
    }

    public function any($handler): TelegramRoute
    {
        return $this->add('any', '*', $handler);
    }

    public function fallback($handler): void
    {
        $this->routes['fallback'] = $this->wrapRoute('*', $handler);
    }

    /* ---------- Dispatch ---------- */

    public function dispatch(array $update): array
    {
        $updateType = $this->getUpdateType($update);
        DiscordLogHelper::log('Received update: '.$updateType, true);

        // 1) payment
        if ($updateType === 'message' && isset($update['message']['successful_payment'])) {
            $payload = json_decode($update['message']['successful_payment']['invoice_payload'] ?? '[]', true) ?: [];
            $type = $payload['type'] ?? null;

            if ($type && isset($this->routes['payment'][$type])) {
                $ctx = $this->makeBaseCtx($update, $update['message'], null);
                $ctx['kind']   = 'payment';
                $ctx['params'] = $payload;
                return $this->runThrough($this->routes['payment'][$type], $ctx);
            }
            return $this->success();
        }

        // 2) command
        DiscordLogHelper::log('Dispatching update of type '.$updateType, true);
        if ($updateType === 'message' && ($cmd = $this->extractCommand($update['message'] ?? []))) {
            DiscordLogHelper::log('Extracted command: '.$cmd, true);
            DiscordLogHelper::log('Context: ' . json_encode($this->routes), true);
            if (isset($this->routes['command'][$cmd])) {
                $ctx = $this->makeBaseCtx($update, $update['message'], null);
                $ctx['kind'] = 'command';
                return $this->runThrough($this->routes['command'][$cmd], $ctx);
            }
        }

        // 3) callback_query
        if ($updateType === 'callback_query') {
            $data = $update['callback_query']['data'] ?? '';
            if ($match = $this->matchCallback($data)) {
                [$route, $params] = $match;
                $ctx = $this->makeBaseCtx($update, null, $update['callback_query']);
                $ctx['kind']   = 'callback';
                $ctx['params'] = $params;
                return $this->runThrough($route, $ctx);
            }
        }

        // 4/5) state & text
        if ($updateType === 'message' && isset($update['message']['text'])) {
            $ctx = $this->makeBaseCtx($update, $update['message'], null);

            // nếu bạn cần route theo state, có thể lookup nhanh:
            $stateKey = $ctx['user']->state ?? null;
            if ($stateKey && isset($this->routes['state'][$stateKey])) {
                $ctx['kind'] = 'state';
                return $this->runThrough($this->routes['state'][$stateKey], $ctx);
            }

            $text = $update['message']['text'];
            foreach ($this->routes['text'] as $route) {
                $pattern = $route->pattern;
                if (is_string($pattern) && @preg_match($pattern, $text)) {
                    $ctx['kind'] = 'text';
                    return $this->runThrough($route, $ctx);
                }
                if (is_callable($pattern) && $pattern($text, $update) === true) {
                    $ctx['kind'] = 'text';
                    return $this->runThrough($route, $ctx);
                }
            }
        }

        // 6) any
        foreach ($this->routes['any'] as $route) {
            $ctx = $this->makeBaseCtx($update, $update['message'] ?? null, $update['callback_query'] ?? null);
            $ctx['kind'] = 'any';
            return $this->runThrough($route, $ctx);
        }

        // 7) fallback
        if ($this->routes['fallback']) {
            $ctx = $this->makeBaseCtx($update, $update['message'] ?? null, $update['callback_query'] ?? null);
            $ctx['kind'] = 'fallback';
            return $this->runThrough($this->routes['fallback'], $ctx);
        }

        return ['success' => true, 'handled' => false];
    }

    /* ---------- Helpers ---------- */

    protected function add(string $type, $pattern, $handler): TelegramRoute
    {
        $route = $this->wrapRoute($pattern, $handler);
        $this->routes[$type][$route->key] = $route;
        return $route;
    }

    protected function wrapRoute($pattern, $handler): TelegramRoute
    {
        $attrs = $this->mergeGroupAttributes([]);
        return new TelegramRoute($this->app, $pattern, $handler, $attrs);
    }

    protected function mergeGroupAttributes(array $attrs): array
    {
        foreach ($this->groupStack as $group) {
            $attrs['namespace'] = trim(($group['namespace'] ?? '').'\\'.($attrs['namespace'] ?? ''), '\\');
            $attrs['middleware'] = array_merge($group['middleware'] ?? [], $attrs['middleware'] ?? []);
            // prefix chỉ áp dụng cho callback pattern nếu bạn dùng kiểu "order:*" → "mod:order:*"
            if (!empty($group['prefix']) && isset($attrs['patternForPrefix']) && is_string($attrs['patternForPrefix'])) {
                $attrs['patternForPrefix'] = $group['prefix'].':'.$attrs['patternForPrefix'];
            }
        }
        return $attrs;
    }

    protected function normalizeCommand(string $cmd): string
    {
        $cmd = ltrim($cmd, '/');
        // hỗ trợ "start@feature" → "start"
        return strtolower(Str::before($cmd, '@'));
    }

    protected function extractCommand(array $message): ?string
    {
        $text = $message['text'] ?? null;
        if (!$text || !Str::startsWith($text, '/')) return null;
        return strtok(ltrim($text, '/'), ' ');
    }

    protected function matchCallback(string $data): ?array
    {
        // pattern kiểu: "order:{id}" hoặc "order:*"
        foreach ($this->routes['callback'] as $route) {
            $pattern = $route->pattern;
            $regex = $this->compileCallbackPatternToRegex($pattern, $route->wheres);
            if (preg_match($regex, $data, $m)) {
                $params = array_filter($m, 'is_string', ARRAY_FILTER_USE_KEY);
                return [$route, $params];
            }
        }
        return null;
    }

    protected function compileCallbackPatternToRegex(string $pattern, array $wheres): string
    {
        // chuyển "order:{id}" → "~^order:(?P<id>[^:]+)$~"
        $regex = preg_replace('/\{(\w+)\}/', '(?P<$1>[^:]+)', $pattern);
        // wildcard "*"
        $regex = str_replace('*', '.*', $regex);
        // whereNumber / whereAlpha…
        foreach ($wheres as $name => $rule) {
            $regex = preg_replace(
                '/\(\?P<' . $name . '>\[\^:\]\+\)/',
                '(?P<' . $name . '>' . $rule . ')',
                $regex
            );
        }
        return '~^' . $regex . '$~';
    }

    protected function runThrough(TelegramRoute $route, array $ctx): array
    {
        $ctxObj = $this->app->make(TelegramContext::class);
        $ctxObj->fill($ctx);
        $this->app->instance(TelegramContext::class, $ctxObj);

        $pipes = array_merge($this->globalMiddleware, $route->middleware);

        $pipe = (new Pipeline($this->app))
            ->send($ctx)
            ->through($pipes)
            ->then(function ($ctx) use ($route, $ctxObj) {
                $ctxObj->fill($ctx);
                return $route->invoke($ctx);
            });

        return is_array($pipe) ? $pipe : ['success' => true, 'result' => $pipe];
    }

    protected function getUpdateType(array $update): string
    {
        return app(WebhookService::class)->getUpdateType($update);
    }

    protected function success(array $extra = []): array
    {
        return array_merge(['success' => true, 'handled' => true], $extra);
    }

    public function setGlobalMiddleware(array $middleware): void
    {
        $this->globalMiddleware = $middleware;
    }

    protected function makeBaseCtx(array $update, ?array $message, ?array $callback): array
    {
        $from   = $message['from'] ?? $callback['from'] ?? null;
        $chatId = $message['chat']['id'] ?? ($callback['message']['chat']['id'] ?? null);
        $messageId = $message['message_id'] ?? ($callback['message']['message_id'] ?? null);

        $user = null;
        if ($from && isset($from['id'])) {
            $user = $this->getOrCreateUser($from);

            if ($chatId) {
                $user->last_chat_id = $chatId;
            }

            if ($messageId) {
                $user->last_message_id = $messageId;
            }

            $user->last_active = now();
            $user->save();

        }


        return [
            'kind'    => 'unknown',
            'update'  => $update,
            'message' => $message,
            'callback'=> $callback,
            'chat_id' => $chatId,
            'user'    => $user,
            'params'  => [],
        ];
    }

    protected function getOrCreateUser(array $telegramUser): TelegramUser
    {
        return TelegramUser::updateOrCreate(
            ['tele_id' => (string) $telegramUser['id']],
            [
                'name' => $telegramUser['first_name'] ?? $telegramUser['username'] ?? 'Unknown',
                'username' => $telegramUser['username'] ?? null,
                'language_code' => $telegramUser['language_code'] ?? null,
            ]
        );
    }
}
