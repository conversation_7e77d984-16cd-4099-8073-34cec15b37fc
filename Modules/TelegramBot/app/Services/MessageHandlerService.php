<?php

namespace Modules\TelegramBot\Services;

use App\Helpers\DiscordLogHelper;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use Modules\TelegramBot\Events\TelegramPaymentSuccessful;
use Mo<PERSON>les\TelegramBot\Events\TelegramPreCheckoutReceived;
use Modules\TelegramBot\Models\TelegramUser;
use Modules\TelegramBot\Services\QueryCallbackDispatcher;
use Modules\TelegramClient\Services\TelegramBotClient;

class MessageHandlerService
{
    protected CommandDispatcher $commandDispatcher;

    protected CheckoutCallbackDispatcher $checkoutCallbackDispatcher;

    public function __construct(CommandDispatcher $commandDispatcher, CheckoutCallbackDispatcher $checkoutCallbackDispatcher)
    {
        $this->commandDispatcher = $commandDispatcher;
        $this->checkoutCallbackDispatcher = $checkoutCallbackDispatcher;
    }

    /**
     * Handle incoming update
     */
    public function handleUpdate(array $update): array
    {
        try {
            $updateType = $this->getUpdateType($update);
            DiscordLogHelper::log('Update type: ' . $updateType);

            switch ($updateType) {
                case 'message':
                    return $this->handleMessage($update['message']);
                case 'callback_query':
                    return $this->handleCallbackQuery($update['callback_query']);
                case 'pre_checkout_query':
                    event(new TelegramPreCheckoutReceived($update));

                    return $this->handleSuccess();
                default:
                    Log::info('Unhandled update type', [
                        'type' => $updateType,
                        'update' => $update,
                    ]);

                    return ['success' => true, 'handled' => false];
            }
        } catch (\Exception $e) {
            Log::error('Error handling update', [
                'update' => $update,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Handle regular message
     */
    protected function handleMessage(array $message): array
    {
        DiscordLogHelper::log('Message received: ' . DiscordLogHelper::formatJson($message));
        $user = $this->getOrCreateUser($message['from']);

        try {
            // Update last_chat_id
            if (isset($message['chat']['id'])) {
                $user->last_chat_id = $message['chat']['id'];
                $user->save();
            }
        } catch (\Exception $e) {
            DiscordLogHelper::error('Error when updating last_chat_id', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'message' => $message,
            ]);
        }

        if ($user->isBlocked()) {
            DiscordLogHelper::log("Blocked user {$user->tele_id} attempted to interact.");

            if ($user->last_chat_id) {
                $botClient = new TelegramBotClient();
                $botClient->sendMessage($user->last_chat_id, 'You are blocked from using this bot.');
            }

            return $this->handleSuccess();
        }
        App::setLocale($user->language_code);

        if (isset($message['successful_payment'])) {
            $payload = $message['successful_payment']['invoice_payload'];
            $payload = json_decode($payload, true);
            if (! $payload || ! isset($payload['type'])) {
                DiscordLogHelper::error('Invalid invoice payload', ['payload' => $message['successful_payment']]);
                return $this->handleSuccess();
            }

            $result = $this->checkoutCallbackDispatcher->dispatch(
                $payload['type'],
                $payload,
                $user
            );

            return $this->handleSuccess($result);
        }

        // Handle commands
        if (isset($message['text']) && str_starts_with($message['text'], '/')) {
            return $this->handleCommand($message, $user);
        }

        // Handle text messages with state (for modules like TelegramIstar)
        if (isset($message['text'])) {
            $stateResult = $this->handleTextWithState($message['text'], $message['chat']['id'], $user);
            if ($stateResult !== null) {
                return $stateResult;
            }
        }

        // For now, just acknowledge non-command messages
        return $this->handleSuccess();
    }

    protected function handleCheckout(array $message): array
    {
        event(new TelegramPaymentSuccessful(['message' => $message]));

        return $this->handleSuccess();
    }

    /**
     * Handle callback query (inline keyboard button press)
     */
    protected function handleCallbackQuery(array $callbackQuery): array
    {
        DiscordLogHelper::log('Callback query received: ' . DiscordLogHelper::formatJson($callbackQuery));

        $user = $this->getOrCreateUser($callbackQuery['from']);
        if ($user->isBlocked()) {
            DiscordLogHelper::log("Blocked user {$user->tele_id} attempted to interact.");

            if ($user->last_chat_id) {
                $botClient = new TelegramBotClient();
                $botClient->sendMessage($user->last_chat_id, 'You are blocked from using this bot.');
            }

            return $this->handleSuccess();
        }

        App::setLocale($user->language_code);

        $dispatcher = new QueryCallbackDispatcher();

        return $dispatcher->dispatch($callbackQuery, $user);
    }

    /**
     * Handle bot commands using CommandDispatcher
     */
    protected function handleCommand(array $message, TelegramUser $user): array
    {
        $text = $message['text'];
        $command = strtok(ltrim($text, '/'), ' ');

        DiscordLogHelper::log('Command received: ' . $command . ' from user ' . $user->tele_id);

        return $this->commandDispatcher->dispatch($command, $message, $user);
    }

    /**
     * Handle text messages with state checking (for modules like TelegramIstar)
     */
    protected function handleTextWithState(string $text, string|int $chatId, TelegramUser $user): ?array
    {
        $stateService = new MessageStateService();
        $state = $stateService->getUserState($user);

        if ($state !== 'default') {
            DiscordLogHelper::log("Handling text with state: {$state} for user {$user->tele_id}");

            $stateHandler = app(TelegramBotService::class)->stateLoader($state);
            if ($stateHandler) {
                $stateHandler->handle($text, $chatId, $user);
                return $this->handleSuccess();
            } else {
                DiscordLogHelper::error("No handler found for state: {$state}");
            }
        }

        return null;
    }

    /**
     * Get or create user from Telegram user data
     */
    protected function getOrCreateUser(array $telegramUser): TelegramUser
    {
        return TelegramUser::updateOrCreate(
            ['tele_id' => (string) $telegramUser['id']],
            [
                'name' => $telegramUser['first_name'] ?? $telegramUser['username'] ?? 'Unknown',
                'username' => $telegramUser['username'] ?? null,
                'language_code' => $telegramUser['language_code'] ?? null,
                'last_active' => now(),
            ]
        );
    }

    /**
     * Determine update type
     */
    protected function getUpdateType(array $update): string
    {
        $types = [
            'message',
            'callback_query',
            'pre_checkout_query',
        ];

        foreach ($types as $type) {
            if (isset($update[$type])) {
                return $type;
            }
        }

        return 'unknown';
    }

    protected function handleSuccess($data = [])
    {
        return [
            'success' => true,
            'handled' => true,
            'data' => $data,
        ];
    }
}
