<?php

namespace Modules\TelegramBot\TelegramCommands;

use Modules\TelegramBot\Enums\BotService;
use Modules\TelegramBot\Services\TelegramBotService;
use Modules\TelegramClient\Services\TelegramBotClient;
use Modules\TelegramBot\Support\Telegram;

class AddBotCommand
{
    public function __construct() {}

    public function __invoke()
    {
        $botClient = new TelegramBotClient;
        $service = new TelegramBotService;

        $chatId = Telegram::chatId();
        $params = Telegram::params();

        if (empty($params) || ! isset($params['token'])) {
            $botClient->sendMessage($chatId, 'Usage: '.$this->help());

            return;
        }

        $botToken = $params['token'] ?? null;
        $module = $params['module'] ?? 'default';

        if ($module !== 'default' && ! BotService::isValidModule($module)) {
            $botClient->sendMessage($chatId, 'Invalid module name. '.$this->help());

            return;
        }

        if (empty($botToken)) {
            $botClient->sendMessage($chatId, 'Invalid bot token. '.$this->help());

            return;
        }

        $exists = \Modules\TelegramBot\Models\TelegramBot::where('token', $botToken)->first();
        if ($exists) {
            $botClient->sendMessage($chatId, 'Bot with this token already exists.');

            return;
        }

        $service->createBotFromToken($botToken, $module);

        $botClient->sendMessage($chatId, 'Bot added successfully.');
    }

    protected function help()
    {
        $modules = BotService::getAvailableModules();
        $modulesList = implode(', ', $modules);

        return '/addbot token=<bot_token> module=<module_name> (available modules: '.$modulesList.')';
    }
}
