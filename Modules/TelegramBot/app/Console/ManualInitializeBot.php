<?php

namespace Modules\TelegramBot\Console;

use Illuminate\Console\Command;
use Mo<PERSON>les\TelegramBot\Models\TelegramBot;

class ManualInitializeBot extends Command
{
    protected $signature = 'telegram:manual-init-bot';

    public function handle()
    {
        $this->info('Manually initializing bots...');

        $bots = TelegramBot::all();

        foreach ($bots as $bot) {
            try {
                $bot->makeCurrent();
                $bot->initializeBot();
                $this->info("Bot with token {$bot->token} initialized.");
            } catch (\Exception $e) {
                $this->error("Failed to initialize bot with token {$bot->token}: ".$e->getMessage());
            }
        }
    }
}
