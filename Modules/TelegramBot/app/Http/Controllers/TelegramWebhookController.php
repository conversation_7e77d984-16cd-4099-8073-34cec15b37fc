<?php

namespace Modules\TelegramBot\Http\Controllers;

use App\Helpers\DiscordLogHelper;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Modules\TelegramBot\Models\TelegramWebhookLog;
use Modules\TelegramBot\Services\MessageHandlerService;
use Modules\TelegramBot\Services\WebhookService;

class TelegramWebhookController extends Controller
{
    protected WebhookService $webhookService;

    protected MessageHandlerService $messageHandler;

    public function __construct(
        WebhookService $webhookService,
        MessageHandlerService $messageHandler,
    ) {
        $this->webhookService = $webhookService;
        $this->messageHandler = $messageHandler;
    }

    /**
     * Handle incoming webhook from Telegram
     */
    public function handleWebhook(Request $request): Response
    {
        if ($request->isMethod('GET')) {
            return response('OK', 200);
        }

        $webhookLog = null;

        try {
            $payload = $request->all();

            $webhookLog = TelegramWebhookLog::create([
                'update_id' => $payload['update_id'] ?? null,
                'update_type' => $this->webhookService->getUpdateType($payload),
                'payload' => $payload,
                'status' => 'pending',
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            $router = app(\Modules\TelegramBot\Core\TelegramRouter::class);
            $router->dispatch($payload);

            $webhookLog->markAsProcessed();

            return response('OK', 200);

        } catch (\Exception $e) {
            DiscordLogHelper::error('Webhook processing error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'payload' => $request->all(),
            ]);

            if ($webhookLog) {
                $webhookLog->markAsFailed($e->getMessage());
            }

            // Always return 200 to prevent Telegram from retrying
            return response('OK', 200);
        }
    }
}
