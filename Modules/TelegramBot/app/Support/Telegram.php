<?php

namespace Modules\TelegramBot\Support;

use Illuminate\Support\Facades\Facade;

/**
 * @method static \Modules\TelegramBot\Models\TelegramUser|null user()
 * @method static void setUser(?\Modules\TelegramBot\Models\TelegramUser $user)
 * @method static string|null chatId()
 * @method static array update()
 * @method static array|null message()
 * @method static array|null callback()
 * @method static array params()
 * @method static string kind()
 * @method static \Modules\TelegramBot\Support\TelegramContext getFacadeRoot()
 *
 * @see \Modules\TelegramBot\Support\TelegramContext
 */

class Telegram extends Facade
{
    protected static function getFacadeAccessor()
    {
        return TelegramContext::class;
    }
}
