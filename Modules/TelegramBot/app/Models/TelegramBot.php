<?php

namespace Modules\TelegramBot\Models;

use App\Helpers\DiscordLogHelper;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Modules\TelegramBot\Services\TelegramBotService;
use Modules\TelegramClient\Services\TelegramBotClient;
use Spatie\Multitenancy\Contracts\IsTenant;
use Spatie\Multitenancy\Models\Concerns\ImplementsTenant;
use Spatie\Multitenancy\Models\Concerns\UsesLandlordConnection;

class TelegramBot extends Model implements IsTenant
{
    use HasFactory;
    use ImplementsTenant;
    use UsesLandlordConnection;

    protected $table = 'telegram_bot';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'token',
        'name',
        'settings',
    ];

    protected static function booted()
    {
        static::creating(function (TelegramBot $tenant) {
            // Tự động tạo database name từ slug
            if (! $tenant->database) {
                $tenant->database = 'bot_'.Str::slug($tenant->name, '_');
            }

            if (! $tenant->secret) {
                $tenant->secret = Str::random(32);
            }
        });

        static::created(function (TelegramBot $tenant) {
            $tenant->createDatabase();
            $tenant->setWebhookUrl();
            $tenant->initializeBot();
        });
    }

    public function createDatabase()
    {
        DB::statement("CREATE DATABASE IF NOT EXISTS `{$this->getDatabaseName()}`");


        // Thực hiện migration cho tenant database
        try {
            // Safely get the current tenant - returns null if no tenant is current
            $currentTenant = static::current();

            TelegramBotService::migrateTenant($this);

            // revert to original tenant
            $currentTenant?->makeCurrent();

            // Optional: Chạy seeder nếu có
            // \Artisan::call('db:seed', [
            //     '--database' => 'tenant',
            //     '--class' => 'TenantSeeder',
            //     '--force' => true
            // ]);

        } catch (\Exception $e) {

            // Có thể rollback tạo database nếu migration thất bại
            DB::statement("DROP DATABASE IF EXISTS `{$this->getDatabaseName()}`");

            $this->delete();

            DiscordLogHelper::error('Failed to migrate tenant database: '.$e->getMessage());

            throw $e;
        }
    }

    public function setWebhookUrl()
    {
        $appUrl = env('APP_URL').'/api/telegram-bot/webhook';
        $client = app(TelegramBotClient::class, [
            'botToken' => $this->token,
        ]);
        $success = $client->setWebhookUrl($appUrl, $this->secret);
        if (! $success) {
            throw new \Exception('Failed to set webhook');
        }
    }

    public function initializeBot() {
        try {
            $currentSettings = json_decode($this->settings, true) ?? [];
            $module = $currentSettings['module'] ?? 'default';
            $initClass = 'Modules\\'.$module.'\\InitBot';
            if (class_exists($initClass)) {
                $botInit = new $initClass();
                if (method_exists($botInit, 'setup')) {
                    $botInit->setup();
                }
            } else {
                DiscordLogHelper::error("Init class {$initClass} does not exist");
            }
        } catch (\Exception $e) {
            DiscordLogHelper::error('TelegramBot Init Error: '.$e->getMessage());
        }
    }
}
