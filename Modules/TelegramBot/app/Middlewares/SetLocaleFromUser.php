<?php

namespace Modules\TelegramBot\Middlewares;

use Mo<PERSON>les\TelegramBot\Models\TelegramUser;
use Modules\TelegramBot\Support\Telegram;
use Illuminate\Support\Facades\App;

class SetLocaleFromUser
{
    /**
     *
     * @param  array $payload
     * @param  \Closure $next
     * @return array
     */
    public function handle($payload, \Closure $next): array
    {
        /** @var TelegramUser|null $user */
        $user = Telegram::user();

        if ($user) {
            App::setLocale($user->language_code);
        } else {
            App::setLocale('en');
        }

        return $next($payload);
    }
}
