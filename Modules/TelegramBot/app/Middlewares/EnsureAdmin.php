<?php

namespace Modules\TelegramBot\Middlewares;

use Mo<PERSON>les\TelegramBot\Models\TelegramUser;
use Modules\TelegramBot\Support\Telegram;

class EnsureAdmin
{
    /**
     *
     * @param  array $payload
     * @param  \Closure $next
     * @return array
     */
    public function handle($payload, \Closure $next): array
    {
        /** @var TelegramUser|null $user */
        $user = Telegram::user();

        if (! $user || ! $user->isAdmin()) {
            $user->sendMessage(__('telegrambot::messages.not_admin'));

            return ['success' => true, 'handled' => true];
        }

        return $next($payload);
    }
}
