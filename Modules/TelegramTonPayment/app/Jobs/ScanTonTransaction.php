<?php

namespace Modules\TelegramTonPayment\Jobs;

use App\Helpers\DiscordLogHelper;
use App\Services\SettingsService;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Modules\TelegramBot\Models\BotSetting;
use Modules\TelegramTonPayment\Models\RawTonTransaction;
use Modules\TelegramTonPayment\Services\TonBlockchainService;
use Illuminate\Contracts\Queue\ShouldBeUnique;

class ScanTonTransaction implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $address;

    public $uniqueFor = 180; // 3 minutes

    /**
     * Create a new job instance.
     */
    public function __construct(string $address) {
        $this->address = $address;
    }

    /**
     * Execute the job.
     */
    public function handle(): void {
        DiscordLogHelper::log('Start scanning address: ' . $this->address);

        $service = app(TonBlockchainService::class);

        $transactions = $service->fetchTransaction($this->address);

        $lastLt = $this->getLastLt();

        $maxLt = $lastLt;

        foreach ($transactions as $tx) {
            $data = $service->parseTransaction($tx);
            $lt = (int) $data['lt'] ?? null;
            $message = $data['message'] ?? '';

            if ($lastLt && $lt && $lt <= $lastLt) {
                continue;
            }

            if ($lt && (!$maxLt || $lt > $maxLt)) {
                $maxLt = $lt;
            }

            if (!$service->isTransactionSuccess($tx)) {
                continue;
            }

            if (!$this->isAcceptedMessage($message)) {
                continue;
            }

            $exist = RawTonTransaction::where('transaction_hash', $data['hash'])->exists();

            if ($exist) {
                continue;
            }

            RawTonTransaction::create([
                'receiver_address' => $this->address,
                'transaction_hash' => $data['hash'],
                'ton_amount_nano' => $data['value'],
                'message' => $message,
                'lt' => $lt,
            ]);
        }

        if ($maxLt && $maxLt !== $lastLt) {
            BotSetting::set('last_lt', $maxLt, 'integer', 'Last processed lt for ton transaction scanning', false);
        }

        DiscordLogHelper::log('Scanned address: ' . $this->address . ', new transactions: ' . count($transactions));
    }

    private function getLastLt(): ?string {
        $settingService = new SettingsService(BotSetting::class);

        try {
            return $settingService->getSetting('last_lt');
        } catch (\Exception $e) {
            return null;
        }
    }

    private function isAcceptedMessage(string $message): bool {
        return str_starts_with($message, 'tx_');
    }
}
