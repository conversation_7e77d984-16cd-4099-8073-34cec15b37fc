<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('withdrawal', function (Blueprint $table) {
            // Change amount column from DECIMAL(18,8) to UNSIGNED BIGINT
            // This supports TON blockchain amounts in nanotons (up to ~18 quintillion)
            $table->unsignedBigInteger('amount')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('withdrawal', function (Blueprint $table) {
            // Revert back to DECIMAL(18,8) if needed
            $table->decimal('amount', 18, 8)->change();
        });
    }
};