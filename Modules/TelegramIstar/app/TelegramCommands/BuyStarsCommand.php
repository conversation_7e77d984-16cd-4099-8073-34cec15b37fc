<?php

namespace Modules\TelegramIstar\TelegramCommands;

use App\Helpers\DiscordLogHelper;
use App\Helpers\TonHelper;
use Modules\TelegramBot\Models\TelegramUser;
use Modules\TelegramBot\TelegramCommands\CommandInterface;
use Mo<PERSON>les\TelegramClient\Services\TelegramBotClient;
use Modules\TelegramIstar\Services\StarShopService;
use Modules\TelegramIstar\Components\BackToMenu;
use App\Services\SettingsService;
use Modules\TelegramIstar\Helpers\IstarHelper;
use Modules\TelegramBot\Services\MessageStateService;
use Modules\TelegramTonPayment\Models\VerificationTransaction;

class BuyStarsCommand implements CommandInterface
{
    protected $starShopService;

    public function __construct()
    {
        $this->starShopService = new StarShopService;
    }

    /**
     * Handle the buy stars command (callback from star shop menu)
     */
    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        try {
            $botClient = new TelegramBotClient;

            $starAmount = $params[0] ?? null;
            $targetUsername = $params[1] ?? null; // For gifting to others

            if (!$starAmount) {
                DiscordLogHelper::error('Invalid star amount: ' . $starAmount);
                $this->sendInvalidAmountMessage($botClient, $chatId);
                return ['success' => true, 'handled' => true];
            }

            DiscordLogHelper::log('Processing buy stars request for user ' . $user->tele_id . ' - Amount: ' . $starAmount . ($targetUsername ? ' - Target: ' . $targetUsername : ''));

            $istarHelper = new IstarHelper();
            $metadata = $istarHelper->buildStarPurchaseMetaData((int)$starAmount, $targetUsername);
            $settingsService = new SettingsService();
            $address = $settingsService->getSetting('ton_wallet_address');

            $tonPrice = $this->starShopService->calculateTonPrice($starAmount);

            $transaction = $user->createVerificationTransaction($address, $tonPrice * 1_000_000_000, $metadata);

            $message = $this->createPaymentConfirmationMessage($starAmount, $user, $targetUsername);
            $keyboard = $this->createPaymentConfirmationKeyboard($transaction);

            $botClient->sendMessage(
                $chatId,
                $message,
                [
                    'reply_markup' => json_encode($keyboard),
                    'parse_mode' => 'Markdown',
                ]
            );

            DiscordLogHelper::log('Payment confirmation sent to user ' . $user->tele_id . ' for ' . $starAmount . ' stars' . ($targetUsername ? ' (gift for @' . $targetUsername . ')' : ''));

            return ['success' => true, 'handled' => true];
        } catch (\Exception $e) {
            DiscordLogHelper::error('BuyStarsCommand failed: ' . $e->getMessage());
            DiscordLogHelper::error('Stack trace: ' . $e->getTraceAsString());

            return ['success' => false, 'handled' => false];
        }
    }

    /**
     * Send invalid amount error message
     */
    private function sendInvalidAmountMessage(TelegramBotClient $botClient, string|int $chatId): void
    {
        $message = "❌ *Invalid Package Selection*\n\n"
            . "The selected star package is not available.\n"
            . "Please return to the star shop and select a valid package.";

        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '🛒 Back to Star Shop',
                        'callback_data' => 'show_star_shop',
                    ],
                ],
                [
                    BackToMenu::make(),
                ],
            ],
        ];

        $botClient->sendMessage(
            $chatId,
            $message,
            [
                'reply_markup' => json_encode($keyboard),
                'parse_mode' => 'Markdown',
            ]
        );
    }

    /**
     * Create payment confirmation message
     */
    /**
     * Create payment confirmation message
     */
    private function createPaymentConfirmationMessage(int $starAmount, TelegramUser $user, ?string $targetUsername = null): string
    {
        $usdPrice = $this->starShopService->calculateUsdPrice($starAmount);
        $tonHelper = new TonHelper();
        $tonRate = $tonHelper->fetchTonPrice();
        $tonPrice = $usdPrice / $tonRate;

        $settingsService = new SettingsService();
        $tonReceiver = $settingsService->getSetting('ton_wallet_address');

        // Generate payment message format: username + usd_price + target (if gifting)
        $username = $user->username ?: "user{$user->tele_id}";
        $paymentMessage = "{$username}{$usdPrice}";
        if ($targetUsername) {
            $paymentMessage .= "_gift_{$targetUsername}";
        }

        // Get user language (default to English)
        $lang = $user->language_code ?? 'en';

        $formattedTonPrice = number_format($tonPrice, 4);
        $formattedTonRate = number_format($tonRate, 4);

        // Create different messages for regular purchase vs gift
        $packageHeader = $targetUsername
            ? "🎁 *Gift Package for @{$targetUsername}*\n\n"
            : "📋 *Selected Package*\n\n";

        $messages = [
            'en' => $packageHeader
                . "⭐ Stars: *{$starAmount}*\n"
                . "💵 Price: *{$formattedTonPrice} TON* (\${$usdPrice})\n"
                . "💎 TON Rate: \${$formattedTonRate}\n\n"
                . "💰 *Payment Options:*\n\n"
                . "🚀 **Quick Pay**: Use the buttons below to open your TON wallet with pre-filled amount and message\n\n"
                . "📋 **Manual Pay**: Send exactly *{$formattedTonPrice} TON* to:\n"
                . "`{$tonReceiver}`\n"
                . "💬 Message: `{$paymentMessage}`\n\n"
                . "⚠️ *Important:* Send the exact amount and message to avoid delays\n\n",

            'ru' => $packageHeader
                . "⭐ Звёзды: *{$starAmount}*\n"
                . "💵 Цена: *{$formattedTonPrice} TON* (\${$usdPrice})\n"
                . "💎 Курс TON: \${$formattedTonRate}\n\n"
                . "💰 *Варианты оплаты:*\n\n"
                . "🚀 **Быстрая оплата**: Используйте кнопки ниже, чтобы открыть TON кошелёк с предзаполненной суммой и сообщением\n\n"
                . "📋 **Ручная оплата**: Отправьте точно *{$formattedTonPrice} TON* на адрес:\n"
                . "`{$tonReceiver}`\n"
                . "💬 Сообщение: `{$paymentMessage}`\n\n"
                . "⚠️ *Важно:* Отправляйте точную сумму во избежание задержек\n\n"
        ];

        return $messages[$lang] ?? $messages['en'];
    }
    /**
     * Create payment confirmation keyboard following button.md specifications
     */
    private function createPaymentConfirmationKeyboard(VerificationTransaction $transaction): array
    {
        $tonLink = TonHelper::generateTonPaymentLink($transaction->address, $transaction->nano_amount, $transaction->verification_code);
        $tonkeeperLink = TonHelper::generateTonWalletLink($transaction->address, $transaction->nano_amount, $transaction->verification_code);

        return [
            'inline_keyboard' => [
                [
                    [
                        'text' => __('messages.buttons.pay_with_ton_wallet'),
                        'url' => $tonLink,
                    ],
                ],
                [
                    [
                        'text' => __('messages.buttons.pay_with_tonkeeper'),
                        'url' => $tonkeeperLink,
                    ],
                ],
                [
                    [
                        'text' => __('messages.buttons.payment_sent'),
                        'callback_data' => 'show_thank_you',
                    ],
                ],
                [
                    [
                        'text' => __('messages.buttons.cancel'),
                        'callback_data' => 'cancel_buy-' . $transaction->id . '-show_star_shop',
                    ],
                ],
            ],
        ];
    }
}
