<?php

namespace Modules\TelegramIstar\TelegramCommands;

use App\Helpers\DiscordLogHelper;
use <PERSON><PERSON>les\TelegramBot\Models\TelegramUser;
use Mo<PERSON>les\TelegramBot\TelegramCommands\CommandInterface;
use <PERSON><PERSON><PERSON>\TelegramIstar\Views\WithdrawalView;
use Mo<PERSON>les\TelegramBot\Services\MessageStateService;

class WithdrawalCommand implements CommandInterface
{
    /**
     * Handle the /withdrawal command
     */
    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        try {
            DiscordLogHelper::log('Processing /withdrawal command for user ' . $user->tele_id);

            // Check if user has stars in their balance
            $starBalance = $user->getBalance('STAR');
            if ($starBalance <= 0) {
                $this->sendNoStarsMessage($chatId);
                return ['success' => true, 'handled' => true];
            }

            // Check if user has a wallet address configured
            // Since there's no wallet address field in TelegramUser, we'll show a message
            // indicating they need to add a wallet address first
            if (!$this->hasWalletAddress($user)) {
                $this->sendNoWalletMessage($user);
                return ['success' => true, 'handled' => true];
            }

            // Show the withdrawal interface
            $view = new WithdrawalView();
            $view->show($chatId, $user);

            return ['success' => true, 'handled' => true];

        } catch (\Exception $e) {
            DiscordLogHelper::error('WithdrawalCommand failed: ' . $e->getMessage());
            DiscordLogHelper::error('Stack trace: ' . $e->getTraceAsString());

            $this->sendErrorMessage($chatId);
            return ['success' => true, 'handled' => true];
        }
    }

    /**
     * Send message when user has no stars
     */
    private function sendNoStarsMessage(string|int $chatId): void
    {
        $botClient = new \Modules\TelegramClient\Services\TelegramBotClient();

        $message = "⭐ <b>Star to TON Exchange</b> ⭐\n\n"
            . "❌ You don't have any stars. Please run <code>/show_deposit</code> to add stars to your account.\n\n"
            . "💡 <i>Tip: You can buy stars and then exchange them for TON!</i>";

        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '💰 Buy Stars',
                        'callback_data' => 'show_buy_stars_menu',
                    ],
                ],
                [
                    [
                        'text' => '🔙 Back to Menu',
                        'callback_data' => 'start',
                    ],
                ],
            ],
        ];

        $botClient->sendMessage(
            $chatId,
            $message,
            [
                'reply_markup' => json_encode($keyboard),
                'parse_mode' => 'HTML',
            ]
        );
    }

    /**
     * Send message when user has no wallet address
     */
    private function sendNoWalletMessage(TelegramUser $user): void
    {
        $botClient = new \Modules\TelegramClient\Services\TelegramBotClient();

        $message = "⭐ <b>Star to TON Exchange</b> ⭐\n\n"
            . "❌ Please add your wallet address first by providing your TON wallet address.\n\n"
            . "Example:\n"
            . "<code>UQALKPk4q6mOGYCQq8ujB1ne4BDQixoG1RZRruodP-AKBu6R</code>\n\n"
            . "<b>Supported formats:</b>\n"
            . "• User-friendly: UQ... or EQ...\n"
            . "• Raw format: 0:hex_string\n"
            . "• Other valid TON formats\n\n"
            . "💡 <i>Your wallet address is needed for withdrawals and payments.</i>";

        $messageStateService = new MessageStateService();
        $messageStateService->setUserState($user, 'add_wallet');

        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '🔙 Back to Menu',
                        'callback_data' => 'start',
                    ],
                ],
            ],
        ];

        $botClient->sendMessage(
            $user->last_chat_id,
            $message,
            [
                'reply_markup' => json_encode($keyboard),
                'parse_mode' => 'HTML',
            ]
        );
    }

    /**
     * Send generic error message
     */
    private function sendErrorMessage(string|int $chatId): void
    {
        $botClient = new \Modules\TelegramClient\Services\TelegramBotClient();

        $message = "❌ An error occurred while processing your withdrawal request. Please try again later.";

        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '🔙 Back to Menu',
                        'callback_data' => 'start',
                    ],
                ],
            ],
        ];

        $botClient->sendMessage(
            $chatId,
            $message,
            [
                'reply_markup' => json_encode($keyboard),
                'parse_mode' => 'HTML',
            ]
        );
    }

    /**
     * Check if user has a wallet address configured
     */
    private function hasWalletAddress(TelegramUser $user): bool
    {
        return !empty($user->wallet_address);
    }
}
