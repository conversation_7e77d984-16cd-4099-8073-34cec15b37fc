<?php

namespace Mo<PERSON>les\TelegramIstar\TelegramCommands;

use App\Helpers\DiscordLogHelper;
use Mo<PERSON>les\TelegramBot\Models\TelegramUser;
use Modules\TelegramBot\Services\MessageContextService;
use Mo<PERSON>les\TelegramBot\Middlewares\AdminMiddleware;
use Modules\TelegramBot\TelegramCommands\CommandInterface;
use Modules\TelegramClient\Services\TelegramBotClient;
use Modules\TelegramIstar\Models\Withdrawal;
use Modules\TelegramIstar\Enums\WithdrawalType;

class RejectWithdrawalCommand implements CommandInterface
{
    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        return AdminMiddleware::check($user, $chatId, function () use ($chatId, $user, $params) {
            $withdrawalId = $params[0] ?? null;
            if (!$withdrawalId) {
                DiscordLogHelper::error('RejectWithdrawal: Missing withdrawal ID in parameters');
                return ['success' => false, 'handled' => false];
            }

            try {
                $withdrawal = Withdrawal::find($withdrawalId);
                if (!$withdrawal) {
                    DiscordLogHelper::error('RejectWithdrawal: Withdrawal not found', ['id' => $withdrawalId]);
                    $this->sendNotFoundMessage($chatId, $withdrawalId);
                    return ['success' => true, 'handled' => true];
                }

                if ($withdrawal->status !== 'pending') {
                    DiscordLogHelper::error('RejectWithdrawal: Withdrawal not pending', [
                        'id' => $withdrawalId,
                        'status' => $withdrawal->status
                    ]);
                    $this->sendInvalidStatusMessage($chatId, $withdrawalId, $withdrawal->status);
                    return ['success' => true, 'handled' => true];
                }

                // Handle balance restoration for exchange_star type
                if ($withdrawal->type === WithdrawalType::STAR_TO_TON_EXCHANGE) {
                    $this->restoreStarBalance($withdrawal);
                }

                // Update withdrawal status
                $withdrawal->update(['status' => 'rejected']);

                DiscordLogHelper::log('Rejected withdrawal ID ' . $withdrawalId . ' by admin ' . $user->tele_id);

                // Send confirmation to admin
                $this->sendRejectionConfirmation($chatId, $withdrawal);

                // Notify user about rejection
                $this->notifyUserRejection($withdrawal);

                // Delete the original notification message
                MessageContextService::deleteOriginalMessage(new TelegramBotClient(), $chatId);

            } catch (\Exception $e) {
                DiscordLogHelper::error('RejectWithdrawal failed: ' . $e->getMessage());
                DiscordLogHelper::error('Stack trace: ' . $e->getTraceAsString());

                $this->sendErrorMessage($chatId);
                return ['success' => false, 'handled' => false];
            }

            return ['success' => true, 'handled' => true];
        });
    }

    /**
     * Restore star balance for exchange_star withdrawals
     */
    private function restoreStarBalance(Withdrawal $withdrawal): void
    {
        try {
            $user = $withdrawal->user;
            $amount = $withdrawal->amount;

            // Restore the star balance
            $user->addBalance($amount, 'STAR', 'Balance restored - withdrawal rejected (ID: ' . $withdrawal->id . ')');

            DiscordLogHelper::log("Restored {$amount} stars to user {$user->tele_id} for rejected withdrawal {$withdrawal->id}");

        } catch (\Exception $e) {
            DiscordLogHelper::error('Failed to restore star balance: ' . $e->getMessage(), [
                'withdrawal_id' => $withdrawal->id,
                'user_id' => $withdrawal->user_id,
                'amount' => $withdrawal->amount
            ]);
            throw $e; // Re-throw to handle in main try-catch
        }
    }

    /**
     * Send rejection confirmation to admin
     */
    private function sendRejectionConfirmation(string|int $chatId, Withdrawal $withdrawal): void
    {
        $botClient = new TelegramBotClient();

        $typeDisplay = $this->getWithdrawalTypeDisplay($withdrawal->type);
        $balanceRestored = $withdrawal->type === WithdrawalType::STAR_TO_TON_EXCHANGE ? "\n• ✅ Star balance restored to user" : "";

        $message = "❌ **Withdrawal Rejected**\n\n"
            . "📋 **Details:**\n"
            . "• ID: #{$withdrawal->id}\n"
            . "• Type: {$typeDisplay}\n"
            . "• Amount: " . number_format($withdrawal->amount, 2) . "\n"
            . "• User: " . ($withdrawal->user->username ? '@' . $withdrawal->user->username : $withdrawal->user->name) . $balanceRestored . "\n\n"
            . "❌ The withdrawal has been rejected and the user has been notified.";

        $botClient->sendMessage(
            $chatId,
            $message,
            [
                'parse_mode' => 'Markdown',
            ]
        );
    }

    /**
     * Notify user about rejection
     */
    private function notifyUserRejection(Withdrawal $withdrawal): void
    {
        try {
            $botClient = new TelegramBotClient();
            $user = $withdrawal->user;

            $typeDisplay = $this->getWithdrawalTypeDisplay($withdrawal->type);
            $balanceInfo = $withdrawal->type === WithdrawalType::STAR_TO_TON_EXCHANGE
                ? "\n\n💰 **Good news:** Your " . number_format($withdrawal->amount, 2) . " stars have been restored to your balance."
                : "";

            $message = "❌ **Withdrawal Rejected**\n\n"
                . "We're sorry, but your withdrawal request has been rejected.\n\n"
                . "📋 **Details:**\n"
                . "• Request ID: #{$withdrawal->id}\n"
                . "• Type: {$typeDisplay}\n"
                . "• Amount: " . number_format($withdrawal->amount, 2) . "\n"
                . "• Status: **Rejected**" . $balanceInfo . "\n\n"
                . "💡 If you have questions about this rejection, please contact our support team.\n\n"
                . "Thank you for your understanding! 🙏";

            $keyboard = [
                'inline_keyboard' => [
                    [
                        [
                            'text' => '💫 Try Again',
                            'callback_data' => 'withdrawal',
                        ],
                        [
                            'text' => '🏠 Back to Menu',
                            'callback_data' => 'start',
                        ],
                    ],
                ],
            ];

            $botClient->sendMessage(
                $user->tele_id,
                $message,
                [
                    'reply_markup' => json_encode($keyboard),
                    'parse_mode' => 'Markdown',
                ]
            );
        } catch (\Exception $e) {
            DiscordLogHelper::error('Failed to notify user about rejection: ' . $e->getMessage());
        }
    }

    /**
     * Send not found message
     */
    private function sendNotFoundMessage(string|int $chatId, int $withdrawalId): void
    {
        $botClient = new TelegramBotClient();
        $message = "❌ Withdrawal ID {$withdrawalId} not found.";
        $botClient->sendMessage($chatId, $message);
    }

    /**
     * Send invalid status message
     */
    private function sendInvalidStatusMessage(string|int $chatId, int $withdrawalId, string $status): void
    {
        $botClient = new TelegramBotClient();
        $message = "❌ Withdrawal ID {$withdrawalId} cannot be rejected. Current status: {$status}";
        $botClient->sendMessage($chatId, $message);
    }

    /**
     * Send error message
     */
    private function sendErrorMessage(string|int $chatId): void
    {
        $botClient = new TelegramBotClient();
        $message = "❌ An error occurred while rejecting the withdrawal. Please try again.";
        $botClient->sendMessage($chatId, $message);
    }

    /**
     * Get withdrawal type display name
     */
    private function getWithdrawalTypeDisplay(string $type): string
    {
        return match ($type) {
            WithdrawalType::STAR_TO_TON_EXCHANGE => 'Star to TON Exchange',
            WithdrawalType::STAR_PURCHASE => 'Star Purchase',
            WithdrawalType::PREMIUM_PURCHASE => 'Premium Purchase',
            default => ucfirst(str_replace('_', ' ', $type)),
        };
    }
}
