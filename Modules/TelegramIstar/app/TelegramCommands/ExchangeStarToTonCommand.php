<?php

namespace Modules\TelegramIstar\TelegramCommands;

use Mo<PERSON>les\TelegramBot\TelegramCommands\CommandInterface;
use Modules\TelegramBot\Models\TelegramUser;
use App\Helpers\DiscordLogHelper;
use App\Helpers\TonHelper;
use Mo<PERSON>les\TelegramIstar\Models\Withdrawal;
use Modules\TelegramIstar\Enums\WithdrawalType;
use Modules\TelegramBot\Services\MessageContextService;
use Modules\TelegramClient\Services\TelegramBotClient;

class ExchangeStarToTonCommand implements CommandInterface
{
    /**
     * Handle the /exchange_star_to_ton command
     */
    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        $stars = $params[0] ?? null;
        if (!$stars) {
            DiscordLogHelper::error('No amount provided for /exchange_star_to_ton command by user ' . $user->tele_id);
            return ['success' => false, 'handled' => true];
        }

        $stars = (int) $stars;

        $settingService = app(\App\Services\SettingsService::class);
        $rate = $settingService->getSetting('star_exchange_rate');
        $tonHelper = new TonHelper();
        $nanoTon = $tonHelper->convertStarToNanoTon($stars, $rate);

        $user->reduceBalance($stars, 'STAR', 'Exchange STAR to TON Holding');

        Withdrawal::create([
            'user_id' => $user->id,
            'status' => 'pending',
            'amount' => $stars,
            'currency' => 'STAR',
            'address' => $user->wallet_address,
            'type' => WithdrawalType::STAR_TO_TON_EXCHANGE,
        ]);

        // Send success message by editing the original message
        $this->sendSuccessMessage($chatId, $user, $stars, $nanoTon);

        DiscordLogHelper::log('Exchange request submitted successfully for user ' . $user->tele_id . ' - ' . $stars . ' stars to ' . $nanoTon . ' nanoTon');

        return ['success' => true, 'handled' => true];
    }

    /**
     * Send success message by editing the original message
     */
    private function sendSuccessMessage(string|int $chatId, TelegramUser $user, int $stars, int $nanoTon): void
    {
        $botClient = new TelegramBotClient();

        $message = "✅ **Exchange Request Submitted Successfully!**\n\n"
            . "📊 **Transaction Details:**\n"
            . "• Stars exchanged: **{$stars} ⭐**\n"
            . "• TON to receive: **" . number_format($nanoTon / 1000000000, 4) . " TON**\n\n"
            . "🔄 Your exchange request has been submitted to our admin team for processing.\n"
            . "⏱️ Processing typically takes 1-2 business days.\n\n"
            . "💫 Thank you for using our service! We appreciate your trust in us.\n\n"
            . "📞 If you have any questions, please contact our support team.";

        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '🔙 Back to Menu',
                        'callback_data' => 'start',
                    ],
                ],
            ],
        ];

        // Edit the original message with new text and keyboard
        $messageId = MessageContextService::getMessageId();
        if ($messageId) {
            $editResult = $botClient->editMessage(
                $chatId,
                $messageId,
                $message,
                [
                    'parse_mode' => 'Markdown',
                    'reply_markup' => json_encode($keyboard),
                ]
            );

            if ($editResult['success']) {
                DiscordLogHelper::log('Successfully edited exchange confirmation message for user ' . $user->tele_id);
            } else {
                DiscordLogHelper::error('Failed to edit exchange confirmation message, sending new message as fallback', [
                    'user_id' => $user->tele_id,
                    'error' => $editResult['error'] ?? 'Unknown error',
                ]);

                // Fallback: send a new message if editing fails
                $botClient->sendMessage(
                    $chatId,
                    $message,
                    [
                        'reply_markup' => json_encode($keyboard),
                        'parse_mode' => 'Markdown',
                    ]
                );
            }
        } else {
            DiscordLogHelper::error('No message ID available for editing exchange confirmation, sending new message', [
                'user_id' => $user->tele_id,
            ]);

            // Fallback: send a new message if no message ID
            $botClient->sendMessage(
                $chatId,
                $message,
                [
                    'reply_markup' => json_encode($keyboard),
                    'parse_mode' => 'Markdown',
                ]
            );
        }
    }
}
