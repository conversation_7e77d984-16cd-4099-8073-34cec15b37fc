<?php

namespace Modules\TelegramIstar\TelegramCommands;

use App\Helpers\DiscordLogHelper;
use <PERSON><PERSON>les\TelegramBot\Models\TelegramUser;
use Mo<PERSON>les\TelegramBot\TelegramCommands\CommandInterface;
use <PERSON><PERSON><PERSON>\TelegramClient\Services\TelegramBotClient;

class AddWalletCommand implements CommandInterface
{
    /**
     * Handle the /add_wallet command
     */
    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        try {
            DiscordLogHelper::log('Processing /add_wallet command for user ' . $user->tele_id);

            // Check if wallet address is provided
            $walletAddress = $params[0] ?? null;
            if (!$walletAddress) {
                $this->sendUsageMessage($chatId);
                return ['success' => true, 'handled' => true];
            }

            // Validate TON wallet address format
            if (!$this->isValidTonAddress($walletAddress)) {
                $this->sendInvalidAddressMessage($chatId, $walletAddress);
                return ['success' => true, 'handled' => true];
            }

            // Save the wallet address
            $this->saveWalletAddress($user, $walletAddress);

            // Send success message
            $this->sendSuccessMessage($chatId, $walletAddress);

            return ['success' => true, 'handled' => true];

        } catch (\Exception $e) {
            DiscordLogHelper::error('AddWalletCommand failed: ' . $e->getMessage());
            DiscordLogHelper::error('Stack trace: ' . $e->getTraceAsString());

            $this->sendErrorMessage($chatId);
            return ['success' => true, 'handled' => true];
        }
    }

    /**
     * Validate TON wallet address format
     */
    private function isValidTonAddress(string $address): bool
    {
        // TON addresses can be in different formats:
        // 1. Raw format: 0:hex_string (64 hex characters)
        // 2. User-friendly format: UQ... or EQ... (48 characters, base64url)
        // 3. Bounceable/non-bounceable variants

        // Remove any whitespace
        $address = trim($address);

        // Check raw format (0:hex_string)
        if (preg_match('/^-?[0-9]:[a-fA-F0-9]{64}$/', $address)) {
            return true;
        }

        // Check user-friendly format (UQ/EQ + 46 base64url characters)
        if (preg_match('/^[UE][Qq][A-Za-z0-9_-]{46}$/', $address)) {
            return true;
        }

        // Check other valid TON address formats
        if (preg_match('/^[kf][A-Za-z0-9_-]{46}$/', $address)) {
            return true;
        }

        return false;
    }

    /**
     * Save wallet address to user
     */
    private function saveWalletAddress(TelegramUser $user, string $walletAddress): void
    {
        $user->update(['wallet_address' => $walletAddress]);
        DiscordLogHelper::log("Saved wallet address for user {$user->tele_id}: {$walletAddress}");
    }

    /**
     * Send usage message
     */
    private function sendUsageMessage(string|int $chatId): void
    {
        $botClient = new TelegramBotClient();

        $message = "💳 **Add Wallet Address** 💳\n\n"
            . "Please provide your TON wallet address.\n\n"
            . "**Usage:** `/add_wallet <your_wallet_address>`\n\n"
            . "**Example:**\n"
            . "/add_wallet UQALKPk4q6mOGYCQq8ujB1ne4BDQixoG1RZRruodP-AKBu6R\n\n"
            . "**Supported formats:**\n"
            . "• User-friendly: UQ... or EQ...\n"
            . "• Raw format: 0:hex_string\n"
            . "• Other valid TON formats\n\n"
            . "💡 *Your wallet address is needed for withdrawals and payments.*";

        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '🔙 Back to Menu',
                        'callback_data' => 'start',
                    ],
                ],
            ],
        ];

        $botClient->sendMessage(
            $chatId,
            $message,
            [
                'reply_markup' => json_encode($keyboard),
                'parse_mode' => 'Markdown',
            ]
        );
    }

    /**
     * Send invalid address message
     */
    private function sendInvalidAddressMessage(string|int $chatId, string $address): void
    {
        $botClient = new TelegramBotClient();

        $message = "❌ **Invalid Wallet Address** ❌\n\n"
            . "The provided address is not a valid TON wallet address:\n"
            . "`{$address}`\n\n"
            . "**Valid TON address formats:**\n"
            . "• User-friendly: `UQAbc...` or `EQAbc...`\n"
            . "• Raw format: `0:1234abcd...`\n\n"
            . "**Please check your address and try again.**\n\n"
            . "💡 *You can copy your address from your TON wallet app.*";

        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '🔄 Try Again',
                        'callback_data' => 'add_wallet_help',
                    ],
                    [
                        'text' => '🔙 Back to Menu',
                        'callback_data' => 'start',
                    ],
                ],
            ],
        ];

        $botClient->sendMessage(
            $chatId,
            $message,
            [
                'reply_markup' => json_encode($keyboard),
                'parse_mode' => 'Markdown',
            ]
        );
    }

    /**
     * Send success message
     */
    private function sendSuccessMessage(string|int $chatId, string $walletAddress): void
    {
        $botClient = new TelegramBotClient();

        $message = "✅ **Wallet Address Added Successfully** ✅\n\n"
            . "Your TON wallet address has been saved:\n"
            . "`{$walletAddress}`\n\n"
            . "🎉 **You can now:**\n"
            . "• Make withdrawals\n"
            . "• Exchange stars for TON\n"
            . "• Receive payments\n\n";
        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '💫 Make Withdrawal',
                        'callback_data' => 'withdrawal',
                    ],
                ],
                [
                    [
                        'text' => '🏠 Back to Menu',
                        'callback_data' => 'start',
                    ],
                ],
            ],
        ];

        $botClient->sendMessage(
            $chatId,
            $message,
            [
                'reply_markup' => json_encode($keyboard),
                'parse_mode' => 'Markdown',
            ]
        );
    }

    /**
     * Send error message
     */
    private function sendErrorMessage(string|int $chatId): void
    {
        $botClient = new TelegramBotClient();

        $message = "❌ An error occurred while saving your wallet address. Please try again later.";

        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '🔙 Back to Menu',
                        'callback_data' => 'start',
                    ],
                ],
            ],
        ];

        $botClient->sendMessage(
            $chatId,
            $message,
            [
                'reply_markup' => json_encode($keyboard),
                'parse_mode' => 'Markdown',
            ]
        );
    }
}
