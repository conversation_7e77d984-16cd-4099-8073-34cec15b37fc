<?php

namespace Modules\TelegramIstar\TelegramCommands;

use App\Helpers\DiscordLogHelper;
use Modules\TelegramBot\Models\TelegramUser;
use Modules\TelegramBot\Services\MessageContextService;
use Mo<PERSON>les\TelegramBot\Middlewares\AdminMiddleware;
use Mo<PERSON>les\TelegramBot\TelegramCommands\CommandInterface;
use Modules\TelegramClient\Services\TelegramBotClient;
use Modules\TelegramIstar\Models\Withdrawal;
use Modules\TelegramIstar\Enums\WithdrawalType;
use App\Helpers\TonHelper;
use App\Services\SettingsService;

class ApproveWithdrawalCommand implements CommandInterface
{
    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        return AdminMiddleware::check($user, $chatId, function () use ($chatId, $user, $params) {
            $withdrawalId = $params[0] ?? null;
            if (!$withdrawalId) {
                DiscordLogHelper::error('ApproveWithdrawal: Missing withdrawal ID in parameters');
                return ['success' => false, 'handled' => false];
            }

            try {
                $withdrawal = Withdrawal::find($withdrawalId);
                if (!$withdrawal) {
                    DiscordLogHelper::error('ApproveWithdrawal: Withdrawal not found', ['id' => $withdrawalId]);
                    $this->sendNotFoundMessage($chatId, $withdrawalId);
                    return ['success' => true, 'handled' => true];
                }

                if ($withdrawal->status !== 'pending') {
                    DiscordLogHelper::error('ApproveWithdrawal: Withdrawal not pending', [
                        'id' => $withdrawalId,
                        'status' => $withdrawal->status
                    ]);
                    $this->sendInvalidStatusMessage($chatId, $withdrawalId, $withdrawal->status);
                    return ['success' => true, 'handled' => true];
                }

                // Update withdrawal status
                $withdrawal->update(['status' => 'approved']);

                DiscordLogHelper::log('Approved withdrawal ID ' . $withdrawalId . ' by admin ' . $user->tele_id);

                // Send confirmation to admin
                $this->sendApprovalConfirmation($chatId, $withdrawal);

                // Notify user about approval
                $this->notifyUserApproval($withdrawal);

                // Delete the original notification message
                MessageContextService::deleteOriginalMessage(new TelegramBotClient(), $chatId);

            } catch (\Exception $e) {
                DiscordLogHelper::error('ApproveWithdrawal failed: ' . $e->getMessage());
                DiscordLogHelper::error('Stack trace: ' . $e->getTraceAsString());

                $this->sendErrorMessage($chatId);
                return ['success' => false, 'handled' => false];
            }

            return ['success' => true, 'handled' => true];
        });
    }

    /**
     * Send approval confirmation to admin
     */
    private function sendApprovalConfirmation(string|int $chatId, Withdrawal $withdrawal): void
    {
        $botClient = new TelegramBotClient();

        $typeDisplay = $this->getWithdrawalTypeDisplay($withdrawal->type);
        $message = "✅ **Withdrawal Approved**\n\n"
            . "📋 **Details:**\n"
            . "• ID: #{$withdrawal->id}\n"
            . "• Type: {$typeDisplay}\n"
            . "• User: " . ($withdrawal->user->username ? '@' . $withdrawal->user->username : $withdrawal->user->name) . "\n\n"
            . "✅ The withdrawal has been approved and the user has been notified.";

        $botClient->sendMessage(
            $chatId,
            $message,
            [
                'parse_mode' => 'Markdown',
            ]
        );
    }

    /**
     * Notify user about approval
     */
    private function notifyUserApproval(Withdrawal $withdrawal): void
    {
        try {
            $botClient = new TelegramBotClient();
            $user = $withdrawal->user;

            $message = $this->buildUserApprovalMessage($withdrawal);
            $keyboard = $this->buildUserApprovalKeyboard($withdrawal);

            $botClient->sendMessage(
                $user->tele_id,
                $message,
                [
                    'reply_markup' => json_encode($keyboard),
                    'parse_mode' => 'HTML',
                ]
            );
        } catch (\Exception $e) {
            DiscordLogHelper::error('Failed to notify user about approval: ' . $e->getMessage());
        }
    }

    /**
     * Build user approval message based on withdrawal type
     */
    private function buildUserApprovalMessage(Withdrawal $withdrawal): string
    {
        switch ($withdrawal->type) {
            case WithdrawalType::STAR_PURCHASE:
                return $this->buildStarPurchaseApprovalMessage($withdrawal);

            case WithdrawalType::PREMIUM_PURCHASE:
                return $this->buildPremiumPurchaseApprovalMessage($withdrawal);

            case WithdrawalType::STAR_TO_TON_EXCHANGE:
                return $this->buildStarToTonExchangeApprovalMessage($withdrawal);

            default:
                return $this->buildGenericApprovalMessage($withdrawal);
        }
    }

    /**
     * Build star purchase approval message
     */
    private function buildStarPurchaseApprovalMessage(Withdrawal $withdrawal): string
    {
        $starAmount = number_format($withdrawal->amount, 0);
        $targetUsername = $withdrawal->address;

        $message = "✅ <b>Star Purchase Approved!</b>\n\n";
        $message .= "🎉 Great news! Your star purchase request has been approved.\n\n";
        $message .= "📋 <b>Details:</b>\n";
        $message .= "• Request ID: #{$withdrawal->id}\n";
        $message .= "• ⭐ Stars: {$starAmount}\n";
        $message .= "• 📍 Target: {$targetUsername}\n";
        $message .= "• 📊 Status: <b>Approved</b>\n\n";
        $message .= "⭐ The stars will be sent to {$targetUsername} shortly.\n\n";
        $message .= "Thank you for using our service! �";

        return $message;
    }

    /**
     * Build premium purchase approval message
     */
    private function buildPremiumPurchaseApprovalMessage(Withdrawal $withdrawal): string
    {
        $days = number_format($withdrawal->amount, 0);
        $targetUsername = $withdrawal->address;

        $message = "✅ <b>Premium Purchase Approved!</b>\n\n";
        $message .= "🎉 Great news! Your Telegram Premium purchase has been approved.\n\n";
        $message .= "📋 <b>Details:</b>\n";
        $message .= "• Request ID: #{$withdrawal->id}\n";
        $message .= "• 💎 Premium Days: {$days}\n";
        $message .= "• 📍 Target: {$targetUsername}\n";
        $message .= "• 📊 Status: <b>Approved</b>\n\n";
        $message .= "💎 Telegram Premium will be activated for {$targetUsername} shortly.\n\n";
        $message .= "Thank you for using our service! 🚀";

        return $message;
    }

    /**
     * Build star to TON exchange approval message
     */
    private function buildStarToTonExchangeApprovalMessage(Withdrawal $withdrawal): string
    {
        $starAmount = number_format($withdrawal->amount, 0);
        $walletAddress = $withdrawal->address;

        $tonHelper = new TonHelper();
        $settingService = new SettingsService();
        $rate = $settingService->getSetting('star_exchange_rate');
        $nanoTonAmount = $tonHelper->convertStarToNanoTon($withdrawal->amount, $rate);

        $tonAmountFormatted = number_format($nanoTonAmount / 1e9, 4);

        $message = "✅ <b>Star to TON Exchange Approved!</b>\n\n";
        $message .= "🎉 Great news! Your star exchange request has been approved.\n\n";
        $message .= "📋 <b>Details:</b>\n";
        $message .= "• Request ID: #{$withdrawal->id}\n";
        $message .= "• ⭐ Stars Exchanged: {$starAmount}\n";
        $message .= "• 💰 TON Amount: {$tonAmountFormatted} TON\n";
        $message .= "• 📍 Wallet: <code>" . substr($walletAddress, 0, 10) . "..." . substr($walletAddress, -6) . "</code>\n";
        $message .= "• 📊 Status: <b>Approved</b>\n\n";
        $message .= "💎 Your {$tonAmountFormatted} TON will be sent to your wallet shortly.\n\n";
        $message .= "Thank you for using our service! 🚀";

        return $message;
    }

    /**
     * Build generic approval message for unknown types
     */
    private function buildGenericApprovalMessage(Withdrawal $withdrawal): string
    {
        $typeDisplay = $this->getWithdrawalTypeDisplay($withdrawal->type);
        $amount = number_format($withdrawal->amount, 2);

        $message = "✅ <b>Withdrawal Approved!</b>\n\n";
        $message .= "🎉 Great news! Your withdrawal request has been approved.\n\n";
        $message .= "📋 <b>Details:</b>\n";
        $message .= "• Request ID: #{$withdrawal->id}\n";
        $message .= "• Type: {$typeDisplay}\n";
        $message .= "• Amount: {$amount}\n";
        $message .= "• 📍 Address: {$withdrawal->address}\n";
        $message .= "• 📊 Status: <b>Approved</b>\n\n";
        $message .= "Your request will be processed shortly.\n\n";
        $message .= "Thank you for using our service! 🚀";

        return $message;
    }

    /**
     * Build user approval keyboard
     */
    private function buildUserApprovalKeyboard(Withdrawal $withdrawal): array
    {
        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '🏠 Back to Menu',
                        'callback_data' => 'start',
                    ],
                ],
            ],
        ];

        // Add specific buttons based on withdrawal type
        switch ($withdrawal->type) {
            case WithdrawalType::STAR_PURCHASE:
                // Add button to purchase more stars
                array_unshift($keyboard['inline_keyboard'], [
                    [
                        'text' => '⭐ Buy More Stars',
                        'callback_data' => 'buy_stars',
                    ],
                ]);
                break;

            case WithdrawalType::PREMIUM_PURCHASE:
                // Add button to purchase more premium
                array_unshift($keyboard['inline_keyboard'], [
                    [
                        'text' => '💎 Buy More Premium',
                        'callback_data' => 'premium_shop',
                    ],
                ]);
                break;

            case WithdrawalType::STAR_TO_TON_EXCHANGE:
                // Add button to make another exchange
                array_unshift($keyboard['inline_keyboard'], [
                    [
                        'text' => '🔄 Exchange More Stars',
                        'callback_data' => 'withdrawal',
                    ],
                ]);
                break;
        }

        return $keyboard;
    }

    /**
     * Send not found message
     */
    private function sendNotFoundMessage(string|int $chatId, int $withdrawalId): void
    {
        $botClient = new TelegramBotClient();
        $message = "❌ Withdrawal ID {$withdrawalId} not found.";
        $botClient->sendMessage($chatId, $message);
    }

    /**
     * Send invalid status message
     */
    private function sendInvalidStatusMessage(string|int $chatId, int $withdrawalId, string $status): void
    {
        $botClient = new TelegramBotClient();
        $message = "❌ Withdrawal ID {$withdrawalId} cannot be approved. Current status: {$status}";
        $botClient->sendMessage($chatId, $message);
    }

    /**
     * Send error message
     */
    private function sendErrorMessage(string|int $chatId): void
    {
        $botClient = new TelegramBotClient();
        $message = "❌ An error occurred while approving the withdrawal. Please try again.";
        $botClient->sendMessage($chatId, $message);
    }

    /**
     * Get withdrawal type display name
     */
    private function getWithdrawalTypeDisplay(string $type): string
    {
        return match ($type) {
            WithdrawalType::STAR_PURCHASE => 'Star Purchase',
            WithdrawalType::PREMIUM_PURCHASE => 'Premium Purchase',
            WithdrawalType::STAR_TO_TON_EXCHANGE => 'Star to TON Exchange',
            default => ucfirst(str_replace('_', ' ', $type)),
        };
    }
}
