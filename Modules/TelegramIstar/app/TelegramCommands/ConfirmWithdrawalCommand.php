<?php

namespace Modules\TelegramIstar\TelegramCommands;

use App\Helpers\DiscordLogHelper;
use <PERSON><PERSON>les\TelegramBot\Models\TelegramUser;
use Mo<PERSON>les\TelegramBot\TelegramCommands\CommandInterface;
use <PERSON><PERSON><PERSON>\TelegramClient\Services\TelegramBotClient;
use Mo<PERSON>les\TelegramIstar\Models\Withdrawal;
use Modules\TelegramIstar\Enums\WithdrawalType;

class ConfirmWithdrawalCommand implements CommandInterface
{
    /**
     * Handle the withdrawal confirmation
     */
    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        try {
            $amount = $params[0] ?? null;

            if (!$amount || !is_numeric($amount)) {
                DiscordLogHelper::error('ConfirmWithdrawalCommand: Invalid amount parameter');
                $this->sendErrorMessage($chatId);
                return ['success' => true, 'handled' => true];
            }

            $amount = (float) $amount;

            // Validate user still has sufficient balance
            $userBalance = $user->getBalance('STAR');
            if ($amount > $userBalance) {
                $this->sendInsufficientBalanceMessage($chatId, $userBalance);
                return ['success' => true, 'handled' => true];
            }

            $walletAddress = $user->wallet_address;
            if (!$walletAddress) {
                $this->sendNoWalletMessage($chatId);
                return ['success' => true, 'handled' => true];
            }

            DiscordLogHelper::log("Confirming star withdrawal: {$amount} stars for user {$user->tele_id}");

            // Deduct stars from user balance immediately
            $user->reduceBalance($amount, 'STAR', 'Star withdrawal - exchange to TON');

            // Create withdrawal record
            $withdrawal = Withdrawal::create([
                'user_id' => $user->id,
                'status' => 'pending',
                'amount' => $amount,
                'address' => $walletAddress,
                'type' => WithdrawalType::STAR_TO_TON_EXCHANGE,
                'note' => 'Exchange star to TON',
            ]);

            DiscordLogHelper::log("Created withdrawal record ID: {$withdrawal->id} for user {$user->tele_id}");

            // Send success message to user
            $this->sendSuccessMessage($chatId, $amount, $withdrawal->id);

            return ['success' => true, 'handled' => true];

        } catch (\Exception $e) {
            DiscordLogHelper::error('ConfirmWithdrawalCommand failed: ' . $e->getMessage());
            DiscordLogHelper::error('Stack trace: ' . $e->getTraceAsString());

            $this->sendErrorMessage($chatId);
            return ['success' => true, 'handled' => true];
        }
    }

    /**
     * Send success message to user
     */
    private function sendSuccessMessage(string|int $chatId, float $amount, int $withdrawalId): void
    {
        $botClient = new TelegramBotClient();

        $message = "✅ **Withdrawal Request Submitted**\n\n"
            . "🎉 Your withdrawal request has been successfully created!\n\n"
            . "📊 **Details:**\n"
            . "• Request ID: **#{$withdrawalId}**\n"
            . "• Stars exchanged: **" . number_format($amount, 2) . " ⭐**\n"
            . "• Status: **Pending Admin Approval**\n\n"
            . "⏳ **What happens next:**\n"
            . "1. Your stars have been deducted from your balance\n"
            . "2. Our admin will review your request\n"
            . "3. TON will be sent to your wallet after approval\n"
            . "4. You'll receive a notification when processed\n\n"
            . "💡 *You can check your balance anytime with the main menu.*";

        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '🏠 Back to Menu',
                        'callback_data' => 'start',
                    ],
                ],
            ],
        ];

        $botClient->sendMessage(
            $chatId,
            $message,
            [
                'reply_markup' => json_encode($keyboard),
                'parse_mode' => 'Markdown',
            ]
        );
    }

    /**
     * Send insufficient balance message
     */
    private function sendInsufficientBalanceMessage(string|int $chatId, float $userBalance): void
    {
        $botClient = new TelegramBotClient();

        $message = "❌ **Insufficient Balance**\n\n"
            . "Your balance has changed. You now have **" . number_format($userBalance, 2) . " ⭐**.\n\n"
            . "Please try again with a valid amount.";

        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '💫 Try Again',
                        'callback_data' => 'withdrawal',
                    ],
                    [
                        'text' => '🏠 Back to Menu',
                        'callback_data' => 'start',
                    ],
                ],
            ],
        ];

        $botClient->sendMessage(
            $chatId,
            $message,
            [
                'reply_markup' => json_encode($keyboard),
                'parse_mode' => 'Markdown',
            ]
        );
    }

    /**
     * Send no wallet message
     */
    private function sendNoWalletMessage(string|int $chatId): void
    {
        $botClient = new TelegramBotClient();

        $message = "❌ **No Wallet Address**\n\n"
            . "Please add your wallet address first by running `/add_wallet [address]`\n\n"
            . "💡 *Example: `/add_wallet UQALKPk4q6mOGYCQq8ujB1ne4BDQixoG1RZRruodP-AKBu6R`*";

        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '🏠 Back to Menu',
                        'callback_data' => 'start',
                    ],
                ],
            ],
        ];

        $botClient->sendMessage(
            $chatId,
            $message,
            [
                'reply_markup' => json_encode($keyboard),
                'parse_mode' => 'Markdown',
            ]
        );
    }

    /**
     * Send generic error message
     */
    private function sendErrorMessage(string|int $chatId): void
    {
        $botClient = new TelegramBotClient();

        $message = "❌ An error occurred while processing your withdrawal. Please try again later.";

        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '🏠 Back to Menu',
                        'callback_data' => 'start',
                    ],
                ],
            ],
        ];

        $botClient->sendMessage(
            $chatId,
            $message,
            [
                'reply_markup' => json_encode($keyboard),
                'parse_mode' => 'Markdown',
            ]
        );
    }

    /**
     * Get user wallet address
     */
    private function getUserWalletAddress(TelegramUser $user): ?string
    {
        return $user->wallet_address;
    }
}
