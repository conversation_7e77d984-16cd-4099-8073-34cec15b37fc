<?php

namespace Modules\TelegramIstar\Console;

use Illuminate\Console\Command;
use Mo<PERSON>les\TelegramBot\Models\TelegramBot;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Input\InputArgument;
use Modules\TelegramTonPayment\Events\TonTransactionVerified;

class ManualProcessPurchase extends Command
{
    /**
     * The name and signature of the console command.
     *
     * Example usage: php artisan istar:manual-process-purchase {id} {tenant_id}
     */
    protected $signature = 'istar:manual-process-purchase {id : The ID of the transaction to process.} {tenant_id : The ID of the tenant.}';

    /**
     * The console command description.
     */
    protected $description = 'Manually process a purchase transaction by its ID.';

    /**
     * Create a new command instance.
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle() {
        $this->info('Starting manual processing of purchase transaction...');

        $transactionId = (int) $this->argument('id');
        $tenantId = (int) $this->argument('tenant_id');
        $tenant  = TelegramBot::find($tenantId);
        if (!$tenant) {
            $this->error("Tenant with ID {$tenantId} not found.");
            return 1;
        }
        $tenant->makeCurrent();

        TonTransactionVerified::dispatch($transactionId);
    }

    /**
     * Get the console command arguments.
     */
    protected function getArguments(): array
    {
        return [
            ['id', InputArgument::REQUIRED, 'The ID of the transaction to process.'],
            ['tenant_id', InputArgument::REQUIRED, 'The ID of the tenant.'],
        ];
    }

    /**
     * Get the console command options.
     */
    protected function getOptions(): array
    {
        return [
            ['example', null, InputOption::VALUE_OPTIONAL, 'An example option.', null],
        ];
    }
}
