<?php

namespace Modules\TelegramIstar\Console;

use Illum<PERSON>\Console\Command;
use Mo<PERSON>les\TelegramBot\Models\TelegramUser;
use Mo<PERSON>les\TelegramIstar\Models\Withdrawal;
use Mo<PERSON><PERSON>\TelegramIstar\Enums\WithdrawalType;
use <PERSON>ymfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Input\InputArgument;

class TestWithdrawalNotification extends Command
{
    /**
     * The name and signature of the console command.
     *
     * Example usage:
     * php artisan istar:test-withdrawal-notification 1
     * php artisan istar:test-withdrawal-notification 1 --user-id=123456789
     * php artisan istar:test-withdrawal-notification 1 --create-user
     * php artisan istar:test-withdrawal-notification 1 --premium-only
     */
    protected $signature = 'istar:test-withdrawal-notification
                            {tenant_id : The ID of the tenant/bot to test}
                            {--user-id= : Telegram user ID to use for test withdrawals}
                            {--create-user : Create a test user if none exists}
                            {--premium-only : Only test premium purchase withdrawal}
                            {--star-only : Only test star purchase withdrawal}';

    /**
     * The console command description.
     */
    protected $description = 'Test the WithdrawalObserver notification functionality for PREMIUM_PURCHASE and STAR_PURCHASE withdrawal types.';

    /**
     * Create a new command instance.
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🧪 Testing WithdrawalObserver notification functionality...');

        // Get and validate tenant
        $tenantId = (int) $this->argument('tenant_id');
        $tenant = $this->getTenant($tenantId);
        if (!$tenant) {
            $this->error("❌ Tenant with ID {$tenantId} not found.");
            return 1;
        }

        $this->info("🏢 Using tenant: {$tenant->name} (ID: {$tenant->id})");

        // Switch to tenant context
        $tenant->makeCurrent();
        $this->info("🔄 Switched to tenant database: {$tenant->getDatabaseName()}");

        try {
            // Get or create test user
            $user = $this->getOrCreateTestUser();
            if (!$user) {
                $this->error('❌ Failed to get or create test user.');
                return 1;
            }

            $this->info("👤 Using test user: {$user->name} (ID: {$user->tele_id})");

            // Determine which withdrawal types to test
            $withdrawalTypes = $this->getWithdrawalTypesToTest();

            // Create test withdrawals
            foreach ($withdrawalTypes as $type => $config) {
                $this->createTestWithdrawal($user, $type, $config);
            }

            $this->info('✅ Test completed! Check your admin notifications for the withdrawal requests.');
            $this->info('💡 Make sure TELEGRAM_NOTIFY_IDS environment variable is set with admin IDs.');

            return 0;
        } finally {
            // Always revert to landlord connection
            $tenant->landlord()->makeCurrent();
        }
    }

    /**
     * Get the tenant by ID.
     */
    protected function getTenant(int $tenantId): ?\Modules\TelegramBot\Models\TelegramBot
    {
        return \Modules\TelegramBot\Models\TelegramBot::find($tenantId);
    }

    /**
     * Get or create a test user for the withdrawals.
     */
    protected function getOrCreateTestUser(): ?TelegramUser
    {
        // If user ID is provided, try to find existing user
        if ($this->option('user-id')) {
            $user = TelegramUser::where('tele_id', $this->option('user-id'))->first();
            if ($user) {
                return $user;
            }
            $this->warn("⚠️ User with ID {$this->option('user-id')} not found.");
        }

        // If create-user option is set, create a test user
        if ($this->option('create-user')) {
            return $this->createTestUser();
        }

        // Try to find any existing user
        $user = TelegramUser::first();
        if ($user) {
            return $user;
        }

        // If no user exists and create-user is not set, ask user
        if (!$this->option('create-user') && $this->confirm('No test user found. Create a test user?', true)) {
            return $this->createTestUser();
        }

        return null;
    }

    /**
     * Create a test user for testing.
     */
    protected function createTestUser(): TelegramUser
    {
        $this->info('🆕 Creating test user...');

        $user = TelegramUser::create([
            'tele_id' => '123456789', // Test Telegram ID
            'name' => 'Test User',
            'username' => 'testuser',
            'language_code' => 'en',
            'last_chat_id' => '123456789',
            'balance_ton' => 100.0,
            'balance_star' => 1000,
            'last_active' => now(),
        ]);

        $this->info("✅ Created test user: {$user->name} (ID: {$user->tele_id})");
        return $user;
    }

    /**
     * Get withdrawal types to test based on options.
     */
    protected function getWithdrawalTypesToTest(): array
    {
        $types = [];

        if ($this->option('premium-only')) {
            $types[WithdrawalType::PREMIUM_PURCHASE] = [
                'amount' => 30, // 30 days
                'address' => '@testuser_premium',
                'note' => 'Test premium purchase notification'
            ];
        } elseif ($this->option('star-only')) {
            $types[WithdrawalType::STAR_PURCHASE] = [
                'amount' => 500, // 500 stars
                'address' => '@testuser_stars',
                'note' => 'Test star purchase notification'
            ];
        } else {
            // Test both types by default
            $types[WithdrawalType::PREMIUM_PURCHASE] = [
                'amount' => 30, // 30 days
                'address' => '@testuser_premium',
                'note' => 'Test premium purchase notification'
            ];
            $types[WithdrawalType::STAR_PURCHASE] = [
                'amount' => 500, // 500 stars
                'address' => '@testuser_stars',
                'note' => 'Test star purchase notification'
            ];
        }

        return $types;
    }

    /**
     * Create a test withdrawal record.
     */
    protected function createTestWithdrawal(TelegramUser $user, string $type, array $config): void
    {
        $this->info("📝 Creating test withdrawal for type: {$type}");

        try {
            $withdrawal = Withdrawal::create([
                'user_id' => $user->id,
                'status' => 'pending',
                'amount' => $config['amount'],
                'currency' => $this->getCurrencyForType($type),
                'address' => $config['address'],
                'type' => $type,
                'note' => $config['note'],
            ]);

            $this->info("✅ Created withdrawal ID: {$withdrawal->id} for {$type}");
            $this->info("   Amount: {$config['amount']} | Address: {$config['address']}");

        } catch (\Exception $e) {
            $this->error("❌ Failed to create withdrawal for {$type}: {$e->getMessage()}");
        }
    }

    /**
     * Get currency based on withdrawal type.
     */
    protected function getCurrencyForType(string $type): string
    {
        return match ($type) {
            WithdrawalType::STAR_PURCHASE => 'STAR',
            WithdrawalType::PREMIUM_PURCHASE => 'TON',
            default => 'TON'
        };
    }

    /**
     * Get the console command arguments.
     */
    protected function getArguments(): array
    {
        return [
            ['tenant_id', InputArgument::REQUIRED, 'The ID of the tenant/bot to test'],
        ];
    }

    /**
     * Get the console command options.
     */
    protected function getOptions(): array
    {
        return [
            ['user-id', null, InputOption::VALUE_OPTIONAL, 'Telegram user ID to use for test withdrawals', null],
            ['create-user', null, InputOption::VALUE_NONE, 'Create a test user if none exists', null],
            ['premium-only', null, InputOption::VALUE_NONE, 'Only test premium purchase withdrawal', null],
            ['star-only', null, InputOption::VALUE_NONE, 'Only test star purchase withdrawal', null],
        ];
    }
}