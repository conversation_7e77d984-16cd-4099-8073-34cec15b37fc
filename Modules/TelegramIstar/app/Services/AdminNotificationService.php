<?php

namespace Modules\TelegramIstar\Services;

use App\Helpers\DiscordLogHelper;
use App\Helpers\TonHelper;
use Modules\TelegramClient\Services\TelegramBotClient;
use Mo<PERSON>les\TelegramIstar\Models\Withdrawal;
use Mo<PERSON>les\TelegramBot\Models\TelegramUser;
use Modules\TelegramIstar\Enums\WithdrawalType;
use App\Services\SettingsService;

class AdminNotificationService
{
    protected TelegramBotClient $botClient;

    public function __construct()
    {
        $this->botClient = new TelegramBotClient();
    }

    /**
     * Send withdrawal notification to all admins
     *
     * @param Withdrawal $withdrawal
     * @return void
     */
    public function sendWithdrawalNotification(Withdrawal $withdrawal): void
    {
        try {
            $notifyIds = $this->notifyIds();
            if (empty($notifyIds)) {
                DiscordLogHelper::error('No notify IDs configured for withdrawal notifications');
                return;
            }

            $message = $this->buildWithdrawalMessage($withdrawal);
            $keyboard = $this->buildWithdrawalKeyboard($withdrawal);

            $isSent = false;
            foreach ($notifyIds as $notifyId) {
                $receiver = TelegramUser::where('tele_id', $notifyId)->first();

                if (!$receiver || !$receiver->last_chat_id) {
                    DiscordLogHelper::error('Notify ID not found in Telegram users', ['notify_id' => $notifyId]);
                    continue;
                }

                try {
                    $this->botClient->sendMessage(
                        chatId: $receiver->last_chat_id,
                        text: $message,
                        options: [
                            'reply_markup' => json_encode($keyboard),
                            'parse_mode' => 'HTML'
                        ]
                    );


                    $isSent = true;
                } catch (\Exception $e) {
                    DiscordLogHelper::error('Failed to send withdrawal notification', [
                        'withdrawal_id' => $withdrawal->id,
                        'notify_id' => $notifyId,
                        'error' => $e->getMessage()
                    ]);
                }
            }
        } catch (\Exception $e) {
            DiscordLogHelper::error('Failed to send withdrawal notifications', [
                'withdrawal_id' => $withdrawal->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        //backup
        if (!$isSent) {
            $adminIds = env('TELEGRAM_ADMIN_IDS', '');
            $adminIds = array_filter(explode(',', $adminIds));
            foreach ($adminIds as $adminId) {
                $telegramUser = TelegramUser::where('tele_id', $adminId)->first();
                if (!$telegramUser || !$telegramUser->last_chat_id) {
                    continue;
                }

                try {
                    $this->botClient->sendMessage(
                        chatId: $telegramUser->last_chat_id,
                        text: $message,
                        options: [
                            'reply_markup' => json_encode($keyboard),
                            'parse_mode' => 'HTML'
                        ]
                    );

                    $isSent = true;
                } catch (\Exception $e) {
                    DiscordLogHelper::error('Failed to send backup withdrawal notification', [
                        'withdrawal_id' => $withdrawal->id,
                        'admin_id' => $adminId,
                        'error' => $e->getMessage()
                    ]);
                }
            }
        }

        if (!$isSent) {
            DiscordLogHelper::error('Failed to send withdrawal notification to any admin', [
                'withdrawal_id' => $withdrawal->id
            ]);
        }
    }

    /**
     * Get admin IDs from environment variable
     *
     * @return array
     */
    protected function notifyIds(): array
    {
        $notifyIds = env('TELEGRAM_NOTIFY_IDS', '');
        if (empty($notifyIds)) {
            return [];
        }

        return array_filter(explode(',', $notifyIds));
    }

    /**
     * Build withdrawal notification message
     *
     * @param Withdrawal $withdrawal
     * @return string
     */
    protected function buildWithdrawalMessage(Withdrawal $withdrawal): string
    {
        $user = $withdrawal->user;
        $userMention = $user->username ? '@' . $user->username : $user->name;
        $userId = $user->tele_id;
        $createdAt = $withdrawal->created_at->format('Y-m-d H:i:s');

        switch ($withdrawal->type) {
            case WithdrawalType::STAR_PURCHASE:
                return $this->buildStarPurchaseMessage($withdrawal, $userMention, $userId, $createdAt);

            case WithdrawalType::PREMIUM_PURCHASE:
                return $this->buildPremiumPurchaseMessage($withdrawal, $userMention, $userId, $createdAt);

            case WithdrawalType::STAR_TO_TON_EXCHANGE:
                return $this->buildStarToTonExchangeMessage($withdrawal, $userMention, $userId, $createdAt);

            default:
                return $this->buildUnknownWithdrawalMessage($withdrawal, $userMention, $userId, $createdAt);
        }
    }

    /**
     * Build star purchase withdrawal message
     */
    protected function buildStarPurchaseMessage(Withdrawal $withdrawal, string $userMention, string $userId, string $createdAt): string
    {
        $typeDisplay = __('telegramistar::notifications.types.star_purchase');
        $starAmount = number_format($withdrawal->amount, 0);
        $targetUsername = $withdrawal->address; // Username that will receive the stars

        $message = "🔔 <b>NEW {$typeDisplay} REQUEST</b>\n\n";
        $message .= "👤 From: {$userMention} (ID: {$userId})\n";
        $message .= "⭐ Star Amount: {$starAmount}\n";
        $message .= "📍 Target Username: {$targetUsername}\n";
        $message .= "📊 Status: " . ucfirst($withdrawal->status) . "\n";
        $message .= "🕒 Time: {$createdAt}\n\n";

        if ($withdrawal->note) {
            $message .= "📝 Note: {$withdrawal->note}\n\n";
        }

        $message .= __('telegramistar::notifications.withdrawal.action_required');

        return $message;
    }

    /**
     * Build premium purchase withdrawal message
     */
    protected function buildPremiumPurchaseMessage(Withdrawal $withdrawal, string $userMention, string $userId, string $createdAt): string
    {
        $typeDisplay = __('telegramistar::notifications.types.premium_purchase');
        $days = number_format($withdrawal->amount, 0);
        $targetUsername = $withdrawal->address; // Username that will receive the premium

        $message = "🔔 <b>NEW {$typeDisplay} REQUEST</b>\n\n";
        $message .= "👤 From: {$userMention} (ID: {$userId})\n";
        $message .= "💎 Premium Days: {$days}\n";
        $message .= "📍 Target Username: {$targetUsername}\n";
        $message .= "📊 Status: " . ucfirst($withdrawal->status) . "\n";
        $message .= "🕒 Time: {$createdAt}\n\n";

        if ($withdrawal->note) {
            $message .= "📝 Note: {$withdrawal->note}\n\n";
        }

        $message .= __('telegramistar::notifications.withdrawal.action_required');

        return $message;
    }

    /**
     * Build star to TON exchange withdrawal message
     */
    protected function buildStarToTonExchangeMessage(Withdrawal $withdrawal, string $userMention, string $userId, string $createdAt): string
    {
        $settingService = app(SettingsService::class);
        $rate = $settingService->getSetting('star_exchange_rate');
        $typeDisplay = __('telegramistar::notifications.types.star_to_ton_exchange');
        $starAmount = number_format($withdrawal->amount, 0);
        $tonHelper = new TonHelper();
        $walletAddress = $withdrawal->address;
        $nanoTonAmount = $tonHelper->convertStarToNanoTon($withdrawal->amount, $rate);
        $tonAmount = number_format($nanoTonAmount / 1_000_000_000, 4);

        $message = "🔔 <b>NEW {$typeDisplay} REQUEST</b>\n\n";
        $message .= "👤 From: {$userMention} (ID: {$userId})\n";
        $message .= "⭐ Star Amount: {$starAmount}\n";
        $message .= "💰 TON Amount: {$tonAmount} TON\n";
        $message .= "📍 Wallet Address: <code>{$walletAddress}</code>\n";
        $message .= "🕒 Time: {$createdAt}\n\n";

        if ($withdrawal->note) {
            $message .= "📝 Note: {$withdrawal->note}\n\n";
        }

        // Add quick action link for TON transfer
        $tonPaymentLink = TonHelper::generateTonPaymentLink($walletAddress, $nanoTonAmount, "Star exchange #{$withdrawal->id}");
        $tonWalletLink = TonHelper::generateTonWalletLink($walletAddress, $nanoTonAmount, "Star exchange #{$withdrawal->id}");

        $message .= "🚀 <b>Quick Actions:</b>\n";
        $message .= "• <a href=\"{$tonPaymentLink}\">Send via TON Wallet</a>\n";
        $message .= "• <a href=\"{$tonWalletLink}\">Send via Tonkeeper</a>\n\n";

        $message .= __('telegramistar::notifications.withdrawal.action_required');

        return $message;
    }

    /**
     * Build unknown withdrawal type message
     */
    protected function buildUnknownWithdrawalMessage(Withdrawal $withdrawal, string $userMention, string $userId, string $createdAt): string
    {
        $typeDisplay = __('telegramistar::notifications.types.unknown');
        $amount = number_format($withdrawal->amount, 2);

        $message = "🔔 <b>NEW {$typeDisplay} REQUEST</b>\n\n";
        $message .= "👤 From: {$userMention} (ID: {$userId})\n";
        $message .= "💰 Amount: {$amount}\n";
        $message .= "📍 Address: {$withdrawal->address}\n";
        $message .= "🔧 Type: {$withdrawal->type}\n";
        $message .= "📊 Status: " . ucfirst($withdrawal->status) . "\n";
        $message .= "🕒 Time: {$createdAt}\n\n";

        if ($withdrawal->note) {
            $message .= "📝 Note: {$withdrawal->note}\n\n";
        }

        $message .= __('telegramistar::notifications.withdrawal.action_required');

        return $message;
    }

    /**
     * Build withdrawal action keyboard
     *
     * @param Withdrawal $withdrawal
     * @return array
     */
    protected function buildWithdrawalKeyboard(Withdrawal $withdrawal): array
    {
        return [
            'inline_keyboard' => [
                [
                    [
                        'text' => __('telegramistar::notifications.buttons.approve'),
                        'callback_data' => "approve_withdrawal-{$withdrawal->id}"
                    ],
                    [
                        'text' => __('telegramistar::notifications.buttons.reject'),
                        'callback_data' => "reject_withdrawal-{$withdrawal->id}"
                    ]
                ]
            ]
        ];
    }
}
