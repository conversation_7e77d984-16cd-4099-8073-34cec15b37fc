<?php

namespace Modules\TelegramIstar\Helpers;

use <PERSON><PERSON><PERSON>\TelegramBot\Models\TelegramUser;
use Mo<PERSON>les\TelegramIstar\Models\Withdrawal;
use Modules\TelegramIstar\Enums\VipLevel;
use Modules\TelegramIstar\Enums\WithdrawalType;

class IstarHelper
{
    public function getVipInfo(TelegramUser $user): array
    {
        $starBalance = $user->balance_star ?? 0;
        $vipLevel = VipLevel::getLevelFromBalance($starBalance);
        $vipEmoji = VipLevel::getEmojiForLevel($vipLevel);

        return [
            'vip_level' => $vipLevel,
            'vip_points' => $starBalance,
            'vip_emoji' => $vipEmoji,
        ];
    }

    public function buildStarPurchaseMetaData(int $amount, ?string $targetUsername = null): array
    {
        $metadata = [
            'type' => WithdrawalType::STAR_PURCHASE,
            'amount' => $amount,
        ];

        if ($targetUsername) {
            $metadata['target_username'] = $targetUsername;
        }

        return $metadata;
    }

    public function buildPremiumPurchaseMetaData(int $days, ?string $targetUsername = null): array
    {
        $metadata = [
            'type' => WithdrawalType::PREMIUM_PURCHASE,
            'days' => $days,
        ];

        if ($targetUsername) {
            $metadata['target_username'] = $targetUsername;
        }

        return $metadata;
    }

    // Process purchase based on metadata and create withdrawal requests by TON
    public function processPurchase(TelegramUser $user, array $payload): void
    {
        $type = $payload['type'] ?? null;
        switch ($type) {
            case WithdrawalType::STAR_PURCHASE:
                $amount = $payload['amount'] ?? 0;
                $targetUsername = $payload['target_username'] ?? $user->username;

                if ($amount > 0) {
                    Withdrawal::create([
                        'user_id' => $user->id,
                        'status' => 'pending',
                        'amount' => $amount,
                        'currency' => 'STAR',
                        'address' => '@' . $targetUsername,
                        'type' => WithdrawalType::STAR_PURCHASE,
                    ]);
                }
                break;
            case WithdrawalType::PREMIUM_PURCHASE:
                $days = $payload['days'] ?? 0;
                $targetUsername = $payload['target_username'] ?? null;

                if ($days > 0) {
                    if ($targetUsername) {
                        // Create withdrawal request for gifting Telegram Premium to others
                        Withdrawal::create([
                            'user_id' => $user->id,
                            'status' => 'pending',
                            'amount' => $days,
                            'address' => '@' . $targetUsername,
                            'type' => WithdrawalType::PREMIUM_PURCHASE,
                        ]);
                    } else {
                        // Create withdrawal request for user's own Telegram Premium
                        Withdrawal::create([
                            'user_id' => $user->id,
                            'status' => 'pending',
                            'amount' => $days,
                            'address' => '@' . ($user->username ?: "user{$user->tele_id}"),
                            'type' => WithdrawalType::PREMIUM_PURCHASE,
                        ]);
                    }
                }
                break;
            default:
                // Unknown type, do nothing
                break;
        }
    }
}
