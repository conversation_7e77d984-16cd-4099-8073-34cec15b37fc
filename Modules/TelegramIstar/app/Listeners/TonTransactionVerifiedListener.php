<?php

namespace Modules\TelegramIstar\Listeners;

use App\Helpers\DiscordLogHelper;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Mo<PERSON>les\TelegramIstar\Helpers\IstarHelper;
use Modules\TelegramTonPayment\Events\TonTransactionVerified;
use Modules\TelegramBot\Models\TelegramUser;

class TonTransactionVerifiedListener
{
    // Removed ShouldQueue and InteractsWithQueue for immediate execution

    /**
     * Handle the event.
     */
    public function handle(TonTransactionVerified $event): void
    {
        DiscordLogHelper::log('Handling TonTransactionVerified event for transaction ID: ' . $event->transaction->id, true);
        $transaction = $event->transaction;

        $metaData = $transaction->metadata ? json_decode($transaction->metadata, true) : null;

        $user = TelegramUser::find($transaction->user_id);

        $service = new IstarHelper();

        if ($metaData && isset($metaData['type'])) {
            $service->processPurchase($user, $metaData);
        }
    }
}
