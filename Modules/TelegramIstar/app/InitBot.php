<?php

namespace Modules\TelegramIstar;

use Mo<PERSON>les\TelegramClient\Services\TelegramBotClient;
use App\Helpers\DiscordLogHelper;

class InitBot
{
    public function setup()
    {
        try {
            $client = new TelegramBotClient();

            $commands = [
                ["command" => "start", "description" => "Main Menu"],
                ["command" => "show_deposit", "description" => "Add Funds"],
                ["command" => "withdrawal", "description" => "Withdraw Funds"],
                ["command" => "show_buy_stars_menu", "description" => "Buy Stars"],
                ["command" => "show_buy_premium_menu", "description" => "Buy Telegram Premium"],
                ["command" => "vip_status", "description" => "Check VIP Status"],
                ["command" => "show_wallet", "description" => "Manage TON Wallet"],
                ["command" => "show_contact", "description" => "Contact Support"],
            ];

            $client->setCommands($commands);
        } catch (\Exception $e) {
            DiscordLogHelper::error('Istar Bot Init Error: '.$e->getMessage());
            return;
        }
    }
}
