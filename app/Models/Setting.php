<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Spatie\Multitenancy\Models\Concerns\UsesLandlordConnection;
use App\Traits\SettingUtils;

class Setting extends Model
{
    use UsesLandlordConnection;
    use SettingUtils;

    protected $table = 'settings';

    protected $fillable = [
        'key',
        'value',
        'type',
        'description',
    ];

    protected $casts = [
    ];
}
